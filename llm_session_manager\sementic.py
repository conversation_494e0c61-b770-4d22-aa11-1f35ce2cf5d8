from typing import List, Dict, Optional, Union
from redisvl.extensions.session_manager import (
    SemanticSessionManager,
)

from llm_session_manager.base import BaseLLMSessionManager

class SemanticLLMSessionManager(BaseLLMSessionManager):
    """
    Session manager using RedisVL's SemanticSessionManager for vector search capabilities.
    """

    def _initialize_manager(
        self, prefix: str, session_tag: Optional[str], redis_url: str
    ) -> SemanticSessionManager:
        return SemanticSessionManager(
            name=prefix,
            session_tag=session_tag,
            redis_url=redis_url,
            overwrite=True,
        )

    def get_relevant(
        self,
        prompt: str,
        as_text: bool = False,
        top_k: int = 5,
        fall_back: bool = False,
        session_tag: Optional[str] = None,
        raw: bool = False,
        distance_threshold: Optional[float] = None,
    ) -> Union[List[str], List[Dict[str, str]]]:
        """
        Retrieve semantically relevant messages based on the given prompt.
        """
        return self.session_manager.get_relevant(
            prompt, as_text, top_k, fall_back, session_tag, raw, distance_threshold
        )
