# Initialize the session manager
from sementic import Semantic<PERSON><PERSON>essionManager
from standard import StandardLLMSessionManager

from redisvl.extensions.constants import ROLE_FIELD_NAME, CONTENT_FIELD_NAME

session_manager = StandardLLMSessionManager(
    session_tag="6yasdc47-8d1d-414d-a110-b097148b6020"
)

# Add user-llm conversation messages
session_manager.add_message(
    {ROLE_FIELD_NAME: "user", CONTENT_FIELD_NAME: "This is yasir"}
)

session_manager.add_message(
    {
        ROLE_FIELD_NAME: "llm",
        CONTENT_FIELD_NAME: "Hello, Okay Yasir how is your day going ?",
    }
)
session_manager.add_message(
    {ROLE_FIELD_NAME: "user", CONTENT_FIELD_NAME: "I am having a bad day today."}
)
session_manager.add_message(
    {
        ROLE_FIELD_NAME: "llm",
        CONTENT_FIELD_NAME: "I'm sorry to hear that. Do you want to talk about it?",
    }
)

session_manager.store(
    prompt="I had a bad presentation today.",
    response="I'm sorry to hear that. What went wrong?",
)

# Retrieve recent messages (for debugging)
recent_messages = session_manager.get_recent(top_k=20)
print("Recent Messages:", recent_messages)

# Retrieve messages relevant to a prompt
# relevant_messages = session_manager.get_relevant(
#     prompt="It is a bad day today.",
# )
# print("Relevant Messages:", relevant_messages)

# Drop
# session_manager.drop(
#     "06yasdc47-8d1d-414d-a110-b097148b6020:1739665409.790701"
# )

# Delete the session index
# session_manager.clear()
