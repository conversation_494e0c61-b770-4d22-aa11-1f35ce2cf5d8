{"type": "object", "properties": {"VisualId": {"type": "string", "description": "Ear Tag must not be empty. (Required)"}, "EID": {"type": "string", "description": "Electronic ID of the cattle (optional)."}, "ColorId": {"type": "integer", "enum": [25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "description": "Color ID. Possible values: Yellow=25, <PERSON>=26, <PERSON>=27, <PERSON>=28, <PERSON>=29, <PERSON>=30, <PERSON>=31, <PERSON>=32, <PERSON>=33, <PERSON>=34. (Optional)"}, "AnimalStatusId": {"type": "integer", "enum": [1, 2, 3, 4], "description": "Status of the animal. Required. Possible values: Active=1, Dead=2, Sold=3, Reference=4."}, "AnimalTypeId": {"type": "integer", "enum": [1, 2, 3], "description": "Type of animal. Required. Possible values: Bull=1, Cow=2, Calf=3."}, "AnimalGenderId": {"type": "integer", "enum": [1, 2, 3], "description": "Gender of the animal. Required if AnimalTypeId is 'Calf' (AnimalTypeId=3). Possible values: BullCalf=1, He<PERSON>=2, <PERSON><PERSON>=3."}, "BreedStatusId": {"type": "integer", "enum": [1, 2, 3], "description": "Breeding status. Required if AnimalTypeId is 'Cow' (AnimalTypeId=2). Possible values: Open=1, Exposed=2, Pregnant=3."}, "Pasture": {"type": "string", "description": "Pasture location of the animal. Required."}, "Breed": {"type": "string", "description": "Breed of the animal. Required."}, "BirthDate": {"type": "string", "format": "date", "description": "Birthdate of the animal (optional)."}, "AnimalConceptionTypeId": {"type": "integer", "enum": [1, 2, 3], "description": "Conception type of the animal. Possible values: AI=1, Natural=2, IVF=3. (Optional)"}, "Sire": {"type": "string", "description": "Sir<PERSON> (father) of the animal (optional)."}, "Dam": {"type": "string", "description": "Dam (mother) of the animal (optional)."}, "BirthWeight": {"type": "number", "description": "Birth weight of the animal (optional)."}, "AnimalOwnershipTypeId": {"type": "integer", "enum": [1, 2], "description": "Ownership type. Possible values: Purchased=1, Raised=2. (Optional)"}, "CattleGroup": {"type": "array", "items": {"type": "string"}, "description": "Groups to which the cattle belongs (optional)."}, "WeaningDate": {"type": "string", "format": "date", "description": "Date when the animal was weaned (optional)."}, "WeaningWeight": {"type": "number", "description": "Weaning weight of the animal (optional)."}, "YearlingDate": {"type": "string", "format": "date", "description": "Yearling date of the animal (optional)."}, "YearlingWeight": {"type": "number", "description": "Yearling weight of the animal (optional)."}, "Weight": {"type": "number", "description": "Current weight of the animal (optional)."}, "CastrationType": {"type": "string", "description": "Type of castration (optional)."}, "CastrationDate": {"type": "string", "format": "date", "description": "Date when the animal was castrated (optional)."}, "DeathDate": {"type": "string", "format": "date", "description": "Death date of the animal (optional)."}, "DeathCauses": {"type": "array", "items": {"type": "string"}, "description": "Possible causes of death, if applicable (optional)."}, "PurchaseDate": {"type": "string", "format": "date", "description": "Purchase date of the animal (optional)."}, "PurchasePrice": {"type": "number", "description": "Purchase price of the animal (optional)."}, "OwnersName": {"type": "string", "description": "Name of the current owner of the animal (optional)."}}, "required": ["VisualId", "AnimalStatusId", "AnimalTypeId", "Pasture", "Breed"]}