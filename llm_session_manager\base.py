import os
from abc import ABC, abstractmethod
import time
from typing import List, Dict, Optional, Union
from redisvl.exceptions import RedisModuleVersionError
from redisvl.extensions.session_manager import BaseSessionManager
from redisvl.extensions.constants import ROLE_FIELD_NAME, CONTENT_FIELD_NAME


class BaseLLMSessionManager(ABC):
    """
    Abstract base class for LLM session management. Defines common methods for session handling.
    """

    def __init__(self, session_tag: Optional[str] = None):
        try:
            # Load Redis configuration from environment variables
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6380")
            prefix = os.getenv("REDIS_LLM_KEYS_PREFIX", "LLM_Sessions")

            # Initialize the session manager
            self.session_manager = self._initialize_manager(
                prefix, session_tag, redis_url
            )
        except RedisModuleVersionError as e:
            raise RuntimeError(
                f"You are probably using Redis instead of Redis Stack. Please switch to Redis Stack to proceed.\n{str(e)}"
            )

    @abstractmethod
    def _initialize_manager(
        self,
        prefix: str,
        session_tag: Optional[str],
        redis_url: str,
    ) -> BaseSessionManager:
        """
        Abstract method to initialize the session manager. Must be implemented by child classes.
        """
        pass

    def add_messages(
        self, messages: List[Dict[str, str]], session_tag: Optional[str] = None
    ) -> None:
        """
        Add multiple messages to the session.
        """

        # Add TimeStamp to all messages
        for message in messages:
            self.session_manager.add_messages([message], session_tag)
            time.sleep(
                0.001
            )  # 1 ms delay to ensure each message has a unique timestamp and redis cache key

    def add_message(
        self, message: Dict[str, str], session_tag: Optional[str] = None
    ) -> None:
        """
        Add a single message to the session.
        """
        self.add_messages([message], session_tag)

    def get_recent(
        self,
        top_k: int = 5,
        as_text: bool = False,
        raw: bool = False,
        session_tag: Optional[str] = None,
    ) -> Union[str, List[str]]:
        """
        Retrieve recent messages from the session.
        """
        return self.session_manager.get_recent(top_k, as_text, raw, session_tag)

    def store(
        self, prompt: str, response: str, session_tag: Optional[str] = None
    ) -> None:
        """
        Store a prompt-response pair in the session.
        """
        self.add_messages(
            [
                {ROLE_FIELD_NAME: "user", CONTENT_FIELD_NAME: prompt},
                {ROLE_FIELD_NAME: "assistant", CONTENT_FIELD_NAME: response},
            ],
            session_tag,
        )

    def drop(self, id: Optional[str] = None) -> None:
        """
        Remove a specific message from the session history.
        """
        self.session_manager.drop(id)

    def clear(self) -> None:
        """
        Clear the entire session history.
        """
        self.session_manager.clear()

    def delete(self) -> None:
        """
        Delete the session index and all stored conversations.
        """
        self.session_manager.delete()

    def remove_current_session_history(self, session_tag: Optional[str] = None) -> None:
        """
        Remove the conversation history of the current session tag only.
        """
        if session_tag:
            self.session_manager.clear(session_tag)
        else:
            self.session_manager.clear()
