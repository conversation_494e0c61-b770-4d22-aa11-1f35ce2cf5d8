db_schema_description = """
Where ever you see CattleId it is basically Animals.Id
**AnimalTypes** values Bull,<PERSON>w,Calf 
 CREATE TABLE AnimalTypes (Name varchar,Description text,PRIMARY KEY (Id))
 **AnimalGender** values Bull Calf,<PERSON><PERSON>,Steer 
 CREATE TABLE AnimalGenders (Name varchar,Description text,PRIMARY KEY (Id)) 
**AnimalStatuses** values Active,Dead,Sold,Reference
 CREATE TABLE AnimalStatuses (Name varchar,Description text,PRIMARY KEY (Id))
**BreedStatuses** values Open,Exposed,Pregnant
 CREATE TABLE BreedStatuses (Name varchar,Description text,PRIMARY KEY (Id))
 CREATE TABLE Breeds (Name varchar,Description text,PRIMARY KEY (Id))
**PastureFrequencies** values Monthly, Yearly
 CREATE TABLE PastureFrequencies (Name varchar CHARACTER,Description text CHARACTER,PRIMARY KEY (Id)) 
**PastureOwnerships** values Owned,Rented,Leased, Purchased
 CREATE TABLE PastureOwnerships (Name varchar CHARACTER,Description text CHARACTER,PRIMARY KEY (Id)) 
 CREATE TABLE Pastures (UserId ,Area text,Name varchar,Description text,AreaUnitId int,Cost decimal,PastureFrequencyId int,PastureOwnershipId int,PaymentDate datetime,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (PastureFrequencyId) REFERENCES PastureFrequencies (Id),CONSTRAINT FOREIGN KEY (PastureOwnershipId) REFERENCES PastureOwnerships (Id),CONSTRAINT FOREIGN KEY (AreaUnitId) REFERENCES Units (Id)) 
 CREATE TABLE Animals (AnimalTypeId int,AnimalGenderId int,AnimalStatusId ,BirthDate datetime,BirthWeight decimal,AnimalOwnershipTypeId int,Weight decimal,SalePrice decimal,DeathCauseDate datetime,OwnersName varchar,PurchaseDate datetime,PurchasePrice double,ModifiedOn datetime,ColorId int,VisualId varchar,EID varchar,PastureId int,BreedId int,BreedStatusId int,AnimalConceptionTypeId int,SireId int,DamId int,WeaningDate datetime,WeaningWeight decimal,YearlingDate datetime,YearlingWeight decimal,UserId ,CastrationDate datetime,CastrationTypeId int,WeaningStatusId int,BreedingDate datetime,IsLock tinyint,PriceUnitId int,UnitId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (AnimalGenderId) REFERENCES AnimalGenders (Id),CONSTRAINT FOREIGN KEY (AnimalOwnershipTypeId) REFERENCES AnimalOwnershipTypes (Id),CONSTRAINT FOREIGN KEY (AnimalStatusId) REFERENCES AnimalStatuses (Id),CONSTRAINT FOREIGN KEY (AnimalTypeId) REFERENCES AnimalTypes (Id),CONSTRAINT FOREIGN KEY (ColorId) REFERENCES Units (Id),CONSTRAINT FOREIGN KEY (CastrationTypeId) REFERENCES ActivityCastrationTypes (Id),CONSTRAINT FOREIGN KEY (AnimalConceptionTypeId) REFERENCES AnimalConceptionTypes (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (BreedId) REFERENCES Breeds (Id),CONSTRAINT FOREIGN KEY (BreedStatusId) REFERENCES BreedStatuses (Id),CONSTRAINT FOREIGN KEY (DamId) REFERENCES Animals (Id),CONSTRAINT FOREIGN KEY (SireId) REFERENCES Animals (Id),CONSTRAINT FOREIGN KEY (PastureId) REFERENCES Pastures (Id),CONSTRAINT FOREIGN KEY (PriceUnitId) REFERENCES Units (Id),CONSTRAINT FOREIGN KEY (UnitId) REFERENCES Units (Id),CONSTRAINT FOREIGN KEY (WeaningStatusId) REFERENCES WeaningStatuses (Id))
 CREATE TABLE CattleCattleGroup (AnimalsId ,CattleGroupsId ,PRIMARY KEY (AnimalsId,CattleGroupsId),CONSTRAINT FOREIGN KEY (AnimalsId) REFERENCES Animals (Id),CONSTRAINT FOREIGN KEY (CattleGroupsId) REFERENCES CattleGroups (Id))
 CREATE TABLE CattleGroups (UserId ,Name varchar,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),)
**FinanceTypes** values Income, Expense
 CREATE TABLE FinanceTypes (Name varchar CHARACTER,PRIMARY KEY (Id))
**sub category of FinanceTypes**
 CREATE TABLE FinanceDetails ( UserId int,FinanceTypeId int,Name varchar CHARACTER,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (FinanceTypeId) REFERENCES FinanceTypes (Id))
 CREATE TABLE Finances (FinanceDetailId ,RecordDate datetime,PaymentDate datetime,Notes text CHARACTER,Amount decimal,PriceUnitId ,AdministratedById ,CattleId int,PastureId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (AdministratedById) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (CattleId) REFERENCES Animals (Id),CONSTRAINT FOREIGN KEY (FinanceDetailId) REFERENCES FinanceDetails (Id),CONSTRAINT FOREIGN KEY (PastureId) REFERENCES Pastures (Id),)
**ActivityTypes** values Pasture Movement,New Treatment,Weight Measurement,Sale,Add Group,New Expense,New Income,Castration,Weaning,Breeding,Pregnancy Check,Calving,Yearling,Dead Record,Observation,Promote To Bull,Promote To Cow,Set To Reference,Pasture Maintenance,Group Movement,RecordHeat,BreedingSoundnessExam,SemenCollection,MilkingSession,Feeding,Withdrawal,Vaccination,BodyConditionScore,MilkEvaluation
 CREATE TABLE ActivityTypes (Name varchar,PRIMARY KEY (Id))
 CREATE TABLE ActivitiesPerformed (UserId int,ActivityTypeId int,Notes text,ActivityDate datetime,ActivityBatchId ,CattleId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivityBatchId) REFERENCES ActivityBatches (Id),CONSTRAINT FOREIGN KEY (CattleId) REFERENCES Animals (Id),CONSTRAINT FOREIGN KEY (ActivityTypeId) REFERENCES ActivityTypes (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id))
 CREATE TABLE ActivityCastrations (CastrationDate datetime,ActivityCastrationTypeId int,ActivitiesPerformedId ,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityCastrationTypeId) REFERENCES ActivityCastrationTypes (Id))
**AspNetRoles** values Admin,Veterinarian,Marketing Manager,Worker.Ranch Owner,
 CREATE TABLE AspNetRoles (Name varchar,NormalizedName varchar,RoleCategoryId,PRIMARY KEY (Id),UNIQUE KEY RoleNameIndex (NormalizedName),CONSTRAINT FOREIGN KEY (RoleCategoryId) REFERENCES applicationenums (Id)) 
 CREATE TABLE AspNetUsers (FullName varchar,Address varchar,IsActive tinyint,CellNumber varchar,PhoneNumber varchar,LanguageId int ,UserName varchar,NormalizedUserName varchar,Email varchar,NormalizedEmail varchar,PRIMARY KEY (Id),UNIQUE KEY UserNameIndex (NormalizedUserName),CONSTRAINT FOREIGN KEY (LanguageId) REFERENCES languages (Id),CONSTRAINT FOREIGN KEY (PromoCodeId) REFERENCES promocodes (Id)) 
 CREATE TABLE ActivityYearling (YearlingDate datetime,Weight decimal,ActivityId int,WeightUnitId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (WeightUnitId) REFERENCES units (Id))
 CREATE TABLE ActivityWeightMeasurements (MeasurementDate datetime,Weight decimal,ActivityId int,WeightUnitId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (WeightUnitId) REFERENCES units (Id)) 
 CREATE TABLE ActivityWeaning (WeaningDate datetime,Weight decimal,ActivityId int,WeightUnitId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (WeightUnitId) REFERENCES units (Id)) 
 CREATE TABLE ActivityTreatments (TreatmentDate datetime,TreatmentName varchar,ActivityMedicationId int,ActivityDiagnosisId int,Dosage varchar,ActivityTreatmentRouteId int,ActivityLocationId int,ActivityUserId int,ActivityId int,PaymentDate datetime,TreatmentCost decimal,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityDiagnosisId) REFERENCES activitydiagnoses (Id),CONSTRAINT FOREIGN KEY (ActivityMedicationId) REFERENCES activitymedications (Id),CONSTRAINT FOREIGN KEY (ActivityTreatmentRouteId) REFERENCES activityroutes (Id),CONSTRAINT FOREIGN KEY (ActivityLocationId) REFERENCES activitytreatmentlocations (Id),CONSTRAINT FOREIGN KEY (ActivityUserId) REFERENCES AspNetUsers (Id),) **ActivityTreatmentLocations** values 'Both Ears', 'Both Eyes', 'Left Elbow Pocket', 'Left Ear', 'Left Eye','Left Flank', 'Left Hip', 'Left Shoulder', 'Lower Left Neck', 'Lower Neck (Both Sides)', 'Lower Right Neck', 'Mouth', 'Nose', 'Right Elbow Pocket', 'Right Ear', 'Right Eye', 'Right Flank', 'Right Hip', 'Right Shoulder', 'Top line', 'Upper Left Neck', 'Upper Neck (Both Sides)', 'Upper Right Neck'
 CREATE TABLE ActivityTreatmentLocations (Name varchar,Description text)
 CREATE TABLE ActivitySemenCollections (ActivityDate datetime,SemenVolume decimal,SpermConcentration decimal,MotilityPercentage decimal,MorphologyPercentage decimal,SemenContainerId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (SemenContainerId) REFERENCES semencontainers (Id))
 CREATE TABLE ActivitySales (SaleDate datetime,SalePrice decimal,ActivityId int,SoldTo text CHARACTER,PriceUnitId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (PriceUnitId) REFERENCES units (Id)) **ActivitRoutes** values Intramammary, Intramuscular, Intranasal, Intraocular, Intraruminal, Intrauterine, Intravaginal, Intravenous, Oral, Other, Pour, Subcutaneous, Topical
 CREATE TABLE ActivityRoutes (Name varchar,Description text,PRIMARY KEY (Id))
 CREATE TABLE ActivityRecordHeats (ActivityDate datetime,ActivityEstrusDetectionId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityEstrusDetectionId) REFERENCES applicationenums (Id),)
 CREATE TABLE ActivityPurposes (UserId int,Name varchar,Description text,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityPromotions (PromotionType int,ActivityId int,PromotionDate datetime DEFAULT '0001-01-01 00:00:00.000000',PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id)) 
 CREATE TABLE ActivityPregnancyChecks (CheckDate datetime,PastureResultId int,PregnancyCheckMethodId int,Notes text,PregnancyDuration datetime,ProgesteroneLevel varchar,BreedStatusId int,AnimalGenderId int,BloodTestId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (AnimalGenderId) REFERENCES animalgenders (Id),CONSTRAINT FOREIGN KEY (BloodTestId) REFERENCES bloodtests (Id),CONSTRAINT FOREIGN KEY (BreedStatusId) REFERENCES breedstatuses (Id),CONSTRAINT FOREIGN KEY (PregnancyCheckMethodId) REFERENCES pregnancycheckmethods (Id))
 CREATE TABLE ActivityPastureMovements (PastureId int,PreviousPastureId int,ActivityPurposeId int,ActivityUserId int,MovementDate datetime,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityPurposeId) REFERENCES activitypurposes (Id),CONSTRAINT FOREIGN KEY (ActivityUserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (PastureId) REFERENCES pastures (Id),CONSTRAINT FOREIGN KEY (PreviousPastureId) REFERENCES pastures (Id)) 
 CREATE TABLE ActivityPastureMaintenances (MaintenanceStartDate datetime,MaintenanceEndDate datetime,MaintenancePaymentDate datetime,PastureMaintenanceTypeId int,PastureId int,MaintenanceCost decimal,PriceUnitId int,ActivityUserId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (PastureMaintenanceTypeId) REFERENCES activitypasturemaintenancetypes (Id),CONSTRAINT FOREIGN KEY (ActivityUserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (PastureId) REFERENCES pastures (Id),CONSTRAINT FOREIGN KEY (PriceUnitId) REFERENCES units (Id)) 
 CREATE TABLE ActivityPastureMaintenanceTypes (UserId int,Name varchar CHARACTER,Description text CHARACTER,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityMedications (UserId int,Name varchar,Description text,PRIMARY KEY (Id),UNIQUE KEY IX_ActivityMedications_Name_UserId_OrganizationId (Name,UserId,OrganizationId),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityGroupMovements (GroupId int DEFAULT '0',ActivityPurposeId int,ActivityUserId int,MovementDate datetime,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityPurposeId) REFERENCES activitypurposes (Id),CONSTRAINT FOREIGN KEY (ActivityUserId) REFERENCES AspNetUsers (Id),CONSTRAINT FOREIGN KEY (GroupId) REFERENCES cattlegroups (Id),)
 CREATE TABLE ActivityGroupMovementCattleGroup (ActivityGroupMovementsId int,PreviousGroupsId int,PRIMARY KEY (ActivityGroupMovementsId,PreviousGroupsId),CONSTRAINT FOREIGN KEY (ActivityGroupMovementsId) REFERENCES activitygroupmovements (Id),CONSTRAINT FOREIGN KEY (PreviousGroupsId) REFERENCES cattlegroups (Id)) **ActivityFeedTypes** values Supplements, Grass, Silage, Grain, Hay
 CREATE TABLE ActivityFeedTypes (UserId int,RName varchar CHARACTER,Description text CHARACTER,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id)) 
 CREATE TABLE ActivityFeedings (ActivityDate datetime,QuantityFeed decimal,FeedingCost decimal,ActivityFeedTypeId int,ActivityFeedingMethodId int,ActivityId int,PastureId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityFeedingMethodId) REFERENCES activityfeedingmethods (Id),CONSTRAINT FOREIGN KEY (ActivityFeedTypeId) REFERENCES activityfeedtypes (Id),**ActivityFeedingMethods** values Grazing, AutomaticFeeder, ManualFeeding
 CREATE TABLE ActivityFeedingMethods (UserId int,RName varchar CHARACTER,Description text CHARACTER,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id)) 
 CREATE TABLE ActivityDiagnoses (UserId int,Name varchar,Description text,PRIMARY KEY (Id),UNIQUE KEY IX_ActivityDiagnoses_Name_UserId_OrganizationId (Name,UserId,OrganizationId),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityDeadRecords (DeathDate datetime,DeathCauseId int,DeathDetail text,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (DeathCauseId) REFERENCES deathcauses (Id),) **ActivityCastrationTypes** values Castration Knife, Scalpel, Conventional Knife, Handerson Castration Tool, Emasculator, Manual Twist, Surgical Ligation, Other Surgical, Burdizzo Clamp, Elastrator Rubber Rings, Banders, Other Non Surgical
 CREATE TABLE ActivityCastrationTypes (UserId int,Name varchar,Description text,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityCastrations (CastrationDate datetime,ActivityCastrationTypeId int,ActivityId int,ActivityUserId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityCastrationTypeId) REFERENCES activitycastrationtypes (Id),CONSTRAINT FOREIGN KEY (ActivityUserId) REFERENCES AspNetUsers (Id),) 
 CREATE TABLE ActivityCalvings (CalvingDate datetime,ActivityBreedId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ActivityBreedId) REFERENCES activitybreeds (Id),) 
 CREATE TABLE ActivityCalvingCalves (AnimalId int,ActivityCalvingId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivityCalvingId) REFERENCES activitycalvings (Id),CONSTRAINT FOREIGN KEY (AnimalId) REFERENCES Animals (Id),) 
 CREATE TABLE ActivityBreeds (BreedingDate datetime,TechnicianName varchar,DonorCow int,MethodId int,EndDate datetime,Notes text,ActivityId int,BreedingCost decimal,PaymentDate datetime,SemenContainerId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (SemenContainerId) REFERENCES semencontainers (Id))
 CREATE TABLE ActivityBreedingSoundnessExams (ActivityDate datetime,Veterinarian text CHARACTER,FindingId int,ResultId int,ActivityId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivitiesPerformedId) REFERENCES ActivitiesPerformed (Id),CONSTRAINT FOREIGN KEY (ResultId) REFERENCES applicationenums (Id),CONSTRAINT FOREIGN KEY (FindingId) REFERENCES breedingsoundnessexamfindings (Id),)
 CREATE TABLE ActivityBreedBull (AnimalId int,ActivityBreedId int,PRIMARY KEY (Id),CONSTRAINT FOREIGN KEY (ActivityBreedId) REFERENCES ActivityBreeds (Id),CONSTRAINT FOREIGN KEY (AnimalId) REFERENCES Animals (Id),)
 CREATE TABLE ActivityBehaviorIndicatingHeats (Name varchar CHARACTER,UserId int,RDescription text CHARACTER,PRIMARY KEY (Id)CONSTRAINT FOREIGN KEY (UserId) REFERENCES AspNetUsers (Id)) 
 CREATE TABLE ActivityBehaviorIndicatingHeatActivityRecordHeat (ActivityBehaviorsIndicatingHeatId int,ActivityRecordHeatsId int,PRIMARY KEY (ActivityBehaviorsIndicatingHeatId,ActivityRecordHeatsId),CONSTRAINT FOREIGN KEY (ActivityBehaviorsIndicatingHeatId) REFERENCES activitybehaviorindicatingheats (Id),CONSTRAINT FOREIGN KEY (ActivityRecordHeatsId) REFERENCES activityrecordheats (Id)) 
 
 """