{"type": "object", "properties": {"FinanceTypeId": {"type": "integer", "enum": [1, 2], "description": "Finance Type: 1=Income, 2=Expense", "default": 1}, "FinanceDetail": {"type": "string", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "description": "Finance Details referring to the finance type. Required. 1 = Purchased Cattle(s), 2 = Pasture Maintenance, 3 = Pasture Rent (paid), 4 = Pasture Bought, 5 = Breeding Cost, 6 = Medical Cost,7 = Cattle Feed,8 = Cattle Sales,9 = Pasture Rent (received),10 = Product Sales"}, "RecordDate": {"type": "string", "format": "date", "description": "Record date of the finance transaction. Required."}, "PaymentDate": {"type": "string", "format": "date", "description": "Payment date of the finance transaction. Required."}, "Notes": {"type": "string", "description": "Any additional notes. optional."}, "Amount": {"type": "number", "description": "Amount of the finance transaction. Required."}, "PriceUnitId": {"type": "integer", "enum": [68, 84, 6, 105, 75, 12, 14, 38, 30, 92, 96, 93, 9, 65, 85, 35, 83, 98, 99, 47, 59, 10, 104, 64, 44, 57, 100, 11, 52, 82, 81, 58, 31, 79, 106, 61, 78, 94, 80, 101, 51, 42, 95, 67, 74, 90, 73, 41, 60, 46, 76, 70, 88, 86, 55, 56, 77, 97, 37, 49, 102, 40, 7, 43, 72, 45, 34, 13, 53, 69, 48, 63, 103, 50, 87, 5, 54, 66, 91, 8], "description": "Unit of the price. Possible values: 68= Algerian Dinar, 84= Argentine Peso, 6= Australian Dollar, 105= Azerbaijani Manat, 75= Bahraini Dinar, 12= Bangladeshi Taka, 14= Botswana Pula, 38= Brazilian Real, 30= British Pound Sterling, 92= Brunei Dollar, 96= Bulgarian Lev, 93= Cambodian Riel, 9= Canadian Dollar, 65= Central African CFA Franc, 85= Chilean Peso, 35= Chinese Yuan, 83= Colombian Peso, 98= Croatian Kuna, 99= Czech Koruna, 47= Danish Krone, 59= Egyptian Pound, 10= Euros, 104= Georgian Lari, 64= Ghanaian <PERSON>, 44= Hong Kong Dollar, 57= Hungarian Forint, 100= Icelandic Krona, 11= Indian Rupee, 52= Indonesian Rupiah, 82= Iranian Rial, 81= Iraqi Dinar, 58= Israeli Shekel, 31= Japanese Yen, 79= Jordanian <PERSON>, 106= Kazakhstani <PERSON>, 61= Kenyan Shilling, 78= Kuwaiti Dinar, 94= Lao Kip, 80= Lebanese Pound, 101= Macedonian Denar, 51= Malaysian Ringgit, 42= Mexican Peso, 95= Mongolian Tugrik, 67= Moroccan Dirham, 74= Myanmar Kyat, 90= Namibian Dollar, 73= Nepalese Rupee, 41= New Zealand Dollar, 60= Nigerian Naira, 46= Norwegian Krone, 76= Omani Rial, 70= Pakistani Rupee, 88= Paraguayan <PERSON>, 86= Peruvian Sol, 55= Philippine Peso, 56= Polish Zloty, 77= Qatari Riyal, 97= Romanian Leu, 37= Russian Ruble, 49= Saudi Riyal, 102= Serbian Dinar, 40= Singapore Dollar, 7= South African Rand, 43= South Korean Won, 72= Sri Lankan Rupee, 45= Swedish Krona, 34= Swiss Franc, 13= Tanzanian Shilling, 53= Thai Baht, 69= Tunisian Dinar, 48= Turkish Lira, 63= Ugandan Shilling, 103= Ukrainian Hryvnia, 50= United Arab Emirates Dirham, 87= Uruguayan Peso, 5= US Dollar, 54= Vietnamese Dong, 66= West African CFA Franc, 91= Zambian Kwacha", "default": 5}}, "required": ["FinanceTypeId, FinanceDetail, RecordDate, PaymentDate, Amount, PriceUnitId, AdministratedById"]}