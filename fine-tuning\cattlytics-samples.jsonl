{"messages":[{"role": "system", "content": "You are a SQL query generator for a cattle management system. You understand these tables and their relationships: LiveStock (Id, AnimalTypeId, AnimalGenderId, AnimalStatusId, BirthDate, BirthWeight, AnimalOwnershipTypeId, PurchasePrice, OwnersName, PurchaseDate, DeathCauseDate, SalePrice, Weight, ColorId, IsDeleted), Cattle (Id, VisualId, EID, PastureId, BreedId, AnimalConceptionTypeId, SireId, BreedStatusId, WeaningDate, WeaningWeight, YearlingDate, UserId, CastrationDate, CastrationTypeId, RanchId, YearlingWeight, WeaningStatusId, BreedingDate, UnitId, SerialNumber, IsLock, PriceUnitId, DamId), Pastures (Id, Name, Area, RanchId, UserId, IsDeleted, Cost, PastureFrequencyId, PastureOwnershipId, PaymentDate, AreaPolygon, CentralPoint), AnimalGenders (Id, Name), AnimalStatuses (Id, Name), CattleGroup (Id, UserId, Name, IsDeleted), CattleCattleGroup (AnimalsId, CattleGroupsId), Activities (ActivityBatchId, CattleId, AnimalIdentifier), ActivityBatches (UserId, ActivityTypeId, Notes, ActivityDate), ActivityTypes (Id, Name, AnimalTypeId), ActivityPregnancyChecks (CheckDate, PregnancyCheckMethodId, BreedStatusId, AnimalGenderId, ActivityId), PregnancyCheckMethods (Id, Name), ActivityCastrations (Id, CastrationDate, ActivityCastrationTypeId, ActivityId, ActivityUserId), ActivityCastrationTypes (UserId, Name), AspNetUsers (Id, FullName, IsActive, LanguageId, Email, UserName, IsWelcomeEmailSent), AspNetRoles (Name, Id, RoleCategoryId, NormalizedName), AspNetUserRoles (RoleId, UserId), Ranches (Name, Id, UserId), ActivityWeaning (WeaningDate, Weight, ActivityId, WeightUnitId),ActivityWeightMeasurements (MeasurementDate, Weight, ActivityId, WeightUnitId), RanchUsers (Id, IsActive, RoleId, RanchId, UserId), FinanceTypes(Id, Name), FinanceDetails( Name FinanceTypeId RanchId UserId), Finances( PastureId CattleId PriceUnitId Amount Notes PaymentDate RecordDate FinanceDetailId),ActivityYearling(Id, YearlingDate, Weight, WeightUnitId, ActivityId), ActivityTreatments(Id, TreatmentDate, TreatmentName, ActivityMedicationId, ActivityDiagnosisId, Dosage, ActivityTreatmentRouteId, ActivityLocationId, ActivityId, PaymentDate, TreatmentCost), ActivityMedications(Id, UserId, Name, IsDeleted), SemenContainers(Id, Name, RanchId, UserId), ActivitySemenCollections(ActivityId,SemenContainerId, MorphologyPercentage, MotilityPercentage,SpermConcentration, SemenVolume, Id), ActivityTreatmentLocations(Id, Name), ActivityRoutes(Id,Name ), ActivityDiagnoses(Id. UserId, Name), ActivitySales(PriceUnitId,ActivityId,SoldTo,SalePrice,SaleDate,Id), ActivityRecordHeats(ActivityId,ActivityEstrusDetectionId,ActivityDate,Id), ActivityPastureMovements(Id, ActivityId,MovementDate, ActivityUserId, ActivityPurposeId, PreviousPastureId, PastureId, Id), ActivityPastureMaintenanceTypes(Name,UserId,Id), ActivityCalvings(ActivityId,ActivityBreedId,CalvingDate,Id), ActivityGroupMovementCattleGroup(ActivityGroupMovementsId,PreviousGroupsId), ActivityPurposes(Name, UserId, Id), ActivityCalvingCalves(Id,AnimalId, ActivityCalvingId), ActivityBreedingSoundnessExams(ResultId,ActivityId, FindingId,Veterinarian, ActivityDate, Id), ActivityBreeds(SemenContainerId, PaymentDate, BreedingCost, ActivityId, Notes, EndDate, MethodId, DonorCow, TechnicianName, BreedingDate, Id), BreedingSoundnessExamFindings(RanchId, UserId, Name, Id), ActivityGroupMovements(ActivityId,MovementDate,ActivityUserId,ActivityPurposeId, GroupId,Id), ActivityPastureMaintenances(ActivityId, ActivityUserId, PriceUnitId, MaintenanceCost, PastureId, PastureMaintenanceTypeId, MaintenancePaymentDate, MaintenanceEndDate,MaintenanceStartDate, Id). Note: The Cattle table Id and LiveStock table Id refer to the same entity, so treat them as equivalent when generating SQL queries or interpreting results. Make sure to read the schema of both the Cattle and LiveStock tables because they collectively form a single cattle entity. For example, to identify deleted cattle, you should check the IsDeleted field in the LiveStock table. Additionally, activity-related tables contain information respective to the type of activity. To retrieve information for a specific activity, you should join the ActivityBatches, Activities, and the respective activity table (e.g., ActivityPregnancyChecks or ActivityCastrations). The UserId field in various tables corresponds to AspNetUsers.Id. To retrieve ranch-specific roles or information, join AspNetUserRoles, AspNetRoles, and RanchUsers based on RoleId and UserId. The VisualId in the Cattle table refers to the AnimalIdentifier, which is used to uniquely identify cattle across activities. When users ask for time-based queries like 'this week' or 'this month', ensure that the query filters results based on the appropriate time frame using date functions (e.g., CURDATE() or DATE_SUB()). Finance Types are 'Income' & 'Expense'."},{"role":"assistant","content":"It seems like you're looking to generate SQL queries for a cattle management system based on the provided schema. Please let me know what specific query or operation you would like assistance with, and I can help you formulate it! For example, do you want to retrieve information about LiveStock, filter by status, or perhaps join tables for more complex queries?"}]}
{"messages":[{"role":"user","content":"How many distinct bull calves are there for the given user?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) as TotalBullCalves FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0;"}]}
{"messages":[{"role":"user","content":"How many bull calves are there in each pasture for the given user?"},{"role":"assistant","content":"SELECT p.Name AS PastureName, COUNT(DISTINCT c.Id) as BullCalvesCount FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0 GROUP BY p.Name ORDER BY BullCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"What are the distinct bull calves in each cattle group for the given user, along with their visual IDs."},{"role":"assistant","content":"SELECT cg.Name AS GroupName, COUNT(DISTINCT c.Id) AS BullCalvesCount, GROUP_CONCAT(c.VisualId) AS CalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0 GROUP BY cg.Name ORDER BY BullCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"How many bull calves are unassigned to any group for the given user, and what are their visual IDs?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) AS UnassignedCalvesCount, GROUP_CONCAT(c.VisualId) AS UnassignedCalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL;"}]}
{"messages":[{"role":"user","content":"What is the detailed information of unassigned bull calves for the given user."},{"role":"assistant","content":"SELECT c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL ORDER BY c.VisualId;"}]}
{"messages":[{"role":"user","content":"What is the detailed information (Visual ID, EID, Birth Date, and Pasture Name) of bull calves for the given user, grouped by cattle group (with unassigned calves listed separately), and ordered by group name and Visual ID?"},{"role":"assistant","content":"SELECT COALESCE(cg.Name, 'Unassigned') AS GroupName, c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId LEFT JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Bull Calf') AND `l`.IsDeleted = 0 ORDER BY CASE WHEN cg.Name IS NULL THEN 1 ELSE 0 END, cg.Name, c.VisualId;"}]}
{"messages":[{"role":"user","content":"How many distinct heifer calves are there for the given user?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) as TotalHeiferCalves FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0;"}]}
{"messages":[{"role":"user","content":"How many heifer calves are there in each pasture for the given user?"},{"role":"assistant","content":"SELECT p.Name AS PastureName, COUNT(DISTINCT c.Id) as HeiferCalvesCount FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0 GROUP BY p.Name ORDER BY HeiferCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"What are the distinct heifer calves in each cattle group for the given user, along with their visual IDs."},{"role":"assistant","content":"SELECT cg.Name AS GroupName, COUNT(DISTINCT c.Id) AS HeiferCalvesCount, GROUP_CONCAT(c.VisualId) AS CalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0 GROUP BY cg.Name ORDER BY HeiferCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"How many heifer calves are unassigned to any group for the given user, and what are their visual IDs?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) AS UnassignedCalvesCount, GROUP_CONCAT(c.VisualId) AS UnassignedCalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL;"}]}
{"messages":[{"role":"user","content":"What is the detailed information of unassigned heifer calves for the given user."},{"role":"assistant","content":"SELECT c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL ORDER BY c.VisualId;"}]}
{"messages":[{"role":"user","content":"What is the detailed information (Visual ID, EID, Birth Date, and Pasture Name) of heifer calves for the given user, grouped by cattle group (with unassigned calves listed separately), and ordered by group name and Visual ID?"},{"role":"assistant","content":"SELECT COALESCE(cg.Name, 'Unassigned') AS GroupName, c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId LEFT JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Heifer') AND `l`.IsDeleted = 0 ORDER BY CASE WHEN cg.Name IS NULL THEN 1 ELSE 0 END, cg.Name, c.VisualId;"}]}
{"messages":[{"role":"user","content":"How many distinct steer calves are there for the given user?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) as TotalSteerCalves FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0;"}]}
{"messages":[{"role":"user","content":"How many steer calves are there in each pasture for the given user?"},{"role":"assistant","content":"SELECT p.Name AS PastureName, COUNT(DISTINCT c.Id) as SteerCalvesCount FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0 GROUP BY p.Name ORDER BY SteerCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"What are the distinct steer calves in each cattle group for the given user, along with their visual IDs."},{"role":"assistant","content":"SELECT cg.Name AS GroupName, COUNT(DISTINCT c.Id) AS SteerCalvesCount, GROUP_CONCAT(c.VisualId) AS CalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0 GROUP BY cg.Name ORDER BY SteerCalvesCount DESC;"}]}
{"messages":[{"role":"user","content":"How many steer calves are unassigned to any group for the given user, and what are their visual IDs?"},{"role":"assistant","content":"SELECT COUNT(DISTINCT c.Id) AS UnassignedCalvesCount, GROUP_CONCAT(c.VisualId) AS UnassignedCalvesVisualIds FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL;"}]}
{"messages":[{"role":"user","content":"What is the detailed information of unassigned steer calves for the given user."},{"role":"assistant","content":"SELECT c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0 AND ccg.AnimalsId IS NULL ORDER BY c.VisualId;"}]}
{"messages":[{"role":"user","content":"What is the detailed information (Visual ID, EID, Birth Date, and Pasture Name) of steer calves for the given user, grouped by cattle group (with unassigned calves listed separately), and ordered by group name and Visual ID?"},{"role":"assistant","content":"SELECT COALESCE(cg.Name, 'Unassigned') AS GroupName, c.VisualId, c.EID, l.BirthDate, p.Name AS PastureName FROM `Cattle` AS `c` JOIN `LiveStock` AS `l` ON `c`.Id = `l`.Id JOIN `Pastures` AS `p` ON `c`.PastureId = `p`.Id LEFT JOIN `CattleCattleGroup` AS `ccg` ON `c`.Id = `ccg`.AnimalsId LEFT JOIN `CattleGroups` AS `cg` ON `ccg`.CattleGroupsId = `cg`.Id WHERE `c`.UserId = user_id AND `l`.AnimalGenderId = (SELECT Id FROM AnimalGenders WHERE NAME = 'Steer') AND `l`.IsDeleted = 0 ORDER BY CASE WHEN cg.Name IS NULL THEN 1 ELSE 0 END, cg.Name, c.VisualId;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to get the details of all cattle in my ranch. Include their Id, AnimalGender, AnimalType, AnimalStatus, BirthDate, VisualId, EID, PastureName, Breed, and BreedStatus, while ensuring only non-deleted livestock are included"},{"role":"assistant","content":"SELECT l.Id, ag.Name AS AnimalGender, at.Name AS AnimalType, ast.Name AS AnimalStatus, l.BirthDate, c.VisualId, c.EID, p.Name AS PastureName, b.Name AS Breed, bs.Name AS BreedStatus FROM Cattle c JOIN LiveStock l ON c.Id = l.Id LEFT JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id LEFT JOIN AnimalStatuses ast ON l.AnimalStatusId = ast.Id LEFT JOIN AnimalTypes at ON l.AnimalTypeId = at.Id LEFT JOIN Pastures p ON c.PastureId = p.Id LEFT JOIN Breeds b ON c.BreedId = b.Id LEFT JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id WHERE c.RanchId = ranch_id AND l.IsDeleted = 0;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the details of pregnancy checks performed by a user for cattle with a breed status of 'open'. Include the check date, animal gender, cattle ID, pregnancy check method, and user ID"},{"role":"assistant","content":"SELECT p.CheckDate, ag.Name AS AnimalGender, aa.CattleId, M.Name AS PregnancyCheckMethod, ab.UserId, bs.Name FROM ActivityPregnancyChecks p JOIN Activities aa ON aa.Id = p.ActivityId JOIN ActivityBatches ab ON ab.Id = aa.ActivityBatchId JOIN PregnancyCheckMethods M ON p.PregnancyCheckMethodId = M.Id LEFT JOIN AnimalGenders ag ON p.AnimalGenderId = ag.Id LEFT JOIN BreedStatuses bs ON p.BreedStatusId = bs.Id WHERE p.BreedStatusId = 1 AND ab.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the details of activities performed by a user"},{"role":"assistant","content":"SELECT aat.Name, AB.ActivityDate, A.CattleId, A.AnimalIdentifier, c.VisualId FROM ActivityBatches AB JOIN Activities A ON AB.Id = A.ActivityBatchId JOIN ActivityTypes aat ON aat.Id = AB.ActivityTypeId JOIN Cattle c ON c.Id = A.CattleId WHERE AB.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the visual ID, animal type ID, and owner's name for livestock that has been marked as deleted"},{"role":"assistant","content":"SELECT C.VisualId, L.AnimalTypeId, L.OwnersName FROM LiveStock L JOIN Cattle C ON L.Id = C.Id WHERE L.IsDeleted = 1 AND C.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the castration details of cattle performed by a user on a specific date (_date)."},{"role":"assistant","content":"SELECT A.CattleId, A.AnimalIdentifier, AC.CastrationDate, AC.ActivityCastrationTypeId, DATEDIFF(NOW(), AC.CastrationDate) AS DaysSinceCastration FROM ActivityCastrations AC JOIN Activities A ON AC.ActivityId = A.Id JOIN ActivityBatches AB ON A.ActivityBatchId = AB.Id WHERE AC.CastrationDate = _date AND AB.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the details of pregnancy checks performed by a user for cattle with a breed status of 'exposed'. Include the check date, animal gender, cattle ID, pregnancy check method, and user ID"},{"role":"assistant","content":"SELECT p.CheckDate, ag.Name AS AnimalGender, aa.CattleId, M.Name AS PregnancyCheckMethod, ab.UserId, bs.Name FROM ActivityPregnancyChecks p JOIN Activities aa ON aa.Id = p.ActivityId JOIN ActivityBatches ab ON ab.Id = aa.ActivityBatchId JOIN PregnancyCheckMethods M ON p.PregnancyCheckMethodId = M.Id LEFT JOIN AnimalGenders ag ON p.AnimalGenderId = ag.Id LEFT JOIN BreedStatuses bs ON p.BreedStatusId = bs.Id WHERE p.BreedStatusId = 2 AND ab.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the details of pregnancy checks performed by a user for cattle with a breed status of 'pregnent'. Include the check date, animal gender, cattle ID, pregnancy check method, and user ID"},{"role":"assistant","content":"SELECT p.CheckDate, ag.Name AS AnimalGender, aa.CattleId, M.Name AS PregnancyCheckMethod, ab.UserId, bs.Name FROM ActivityPregnancyChecks p JOIN Activities aa ON aa.Id = p.ActivityId JOIN ActivityBatches ab ON ab.Id = aa.ActivityBatchId JOIN PregnancyCheckMethods M ON p.PregnancyCheckMethodId = M.Id LEFT JOIN AnimalGenders ag ON p.AnimalGenderId = ag.Id LEFT JOIN BreedStatuses bs ON p.BreedStatusId = bs.Id WHERE p.BreedStatusId = 3 AND ab.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the cattle information for a specific cattle."},{"role":"assistant","content":"SELECT P.CheckDate, pm.Name AS PregnancyCheckMethod, b.Name AS BreedStatus, A.AnimalIdentifier, A.CattleId FROM ActivityPregnancyChecks P JOIN Activities A ON P.ActivityId = A.Id JOIN ActivityBatches AB ON A.ActivityBatchId = AB.Id LEFT JOIN PregnancyCheckMethods pm ON pm.Id = P.PregnancyCheckMethodId LEFT JOIN BreedStatuses b ON b.Id = P.BreedStatusId WHERE AB.UserId = user_id AND A.CattleId = cattle_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the details of all bull calves that are due for castration."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) AS AgeInMonths, r.Name AS RanchName, u.FullName AS UserName, s.Name AS STATUS FROM Cattle c JOIN Livestock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes AT ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id LEFT JOIN Activities act ON act.CattleId = c.Id LEFT JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Castration') LEFT JOIN ActivityCastrations ac ON ac.ActivityId = act.Id JOIN AnimalStatuses s ON l.AnimalStatusId = s.Id WHERE at.Name = 'Calf' AND ag.Name = 'Bull Calf' AND TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) >= 2 AND l.IsDeleted = 0 AND u.Id = 72301 AND ac.CastrationDate IS NULL AND s.Name = 'Active';"}]}
{"messages":[{"role":"user","content":"Write query get all my cows that are due for pregnancy check."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, bs.Name AS BreedStatus, at.Name AS AnimalType, TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) AS AgeInMonths, r.Name AS RanchName, u.FullName AS UserName, p.Name AS PastureName FROM Cattle c JOIN Livestock l ON c.Id = l.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id JOIN AnimalTypes AT ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id LEFT JOIN Activities act ON act.CattleId = c.Id LEFT JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Pregnancy Check') LEFT JOIN ActivityPregnancyChecks ap ON ap.ActivityId = act.Id LEFT JOIN Pastures p ON p.Id = c.PastureId JOIN AnimalStatuses s ON l.AnimalStatusId = s.Id WHERE at.Name = 'Cow' AND bs.Name = 'Exposed' AND TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) >= 1 AND l.IsDeleted = 0 AND ap.CheckDate IS NULL AND s.Name = 'Active' AND u.Id = user_id;"}]}
{"messages":[{"role":"user","content":"Write query get all my cows that are due for weaning."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalIdentifier, l.BirthDate, ag.Name AS AnimalGender, att.Name AS AnimalType, TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) AS AgeInMonths, r.Name AS RanchName, u.FullName AS UserName, wm.Weight AS LastWeightMeasurement FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes att ON l.AnimalTypeId = att.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id LEFT JOIN ActivityWeightMeasurements wm ON wm.ActivityId = (SELECT ActivityId FROM Activities WHERE CattleId = c.Id ORDER BY MeasurementDate DESC LIMIT 1) LEFT JOIN Activities act ON act.CattleId = c.Id LEFT JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Weaning') WHERE (TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) > 3 OR (wm.Weight > 200 OR wm.Weight > 450)) AND l.IsDeleted = 0 AND (TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) < 3 AND wm.Weight <= 200 AND ab.Id IS NOT NULL) AND u.Id = u_id;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount spent on expenses for each cattle."},{"role":"assistant","content":"SELECT f.CattleId, SUM(f.Amount) AS TotalAmount, ft.Name AS FinanceTypeName, f.PaymentDate, c.VisualId FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id LEFT JOIN Cattle c ON f.CattleId = c.Id WHERE ft.Name IN ('Expense') AND fd.UserId = user_id GROUP BY f.CattleId, ft.Name, f.PaymentDate, c.VisualId;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount earned from income for each Cattle."},{"role":"assistant","content":"SELECT f.CattleId, SUM(f.Amount) AS TotalAmount, ft.Name AS FinanceTypeName, f.PaymentDate, c.VisualId FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id LEFT JOIN Cattle c ON f.CattleId = c.Id WHERE ft.Name IN ('Income') AND fd.UserId = user_id GROUP BY f.CattleId, ft.Name, f.PaymentDate, c.VisualId;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount spent by a user this month"},{"role":"assistant","content":"SELECT SUM(f.Amount) AS TotalSpent, ft.Name AS FinanceTypeName, f.PaymentDate FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE ft.Name IN ('Expense') AND fd.UserId = user_id AND MONTH(f.PaymentDate) = MONTH(CURDATE()) AND YEAR(f.PaymentDate) = YEAR(CURDATE()) GROUP BY ft.Name, f.PaymentDate;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount earned by a user this month"},{"role":"assistant","content":"SELECT SUM(f.Amount) AS TotalEarned, ft.Name AS FinanceTypeName, f.PaymentDate FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE ft.Name IN ('Income') AND fd.UserId = user_id AND MONTH(f.PaymentDate) = MONTH(CURDATE()) AND YEAR(f.PaymentDate) = YEAR(CURDATE()) GROUP BY ft.Name, f.PaymentDate;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount spent on expenses for each pasture."},{"role":"assistant","content":"SELECT f.PastureId, SUM(f.Amount) AS TotalAmount, ft.Name AS FinanceTypeName, f.PaymentDate, p.Name FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id LEFT JOIN Pastures p ON f.PastureId = p.Id WHERE ft.Name IN ('Expense') AND PastureId IS NOT NULL AND fd.UserId = user_id GROUP BY f.PastureId, ft.Name, f.PaymentDate, p.Name;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total amount earned from income for each pasture."},{"role":"assistant","content":"SELECT f.PastureId, SUM(f.Amount) AS TotalAmount, ft.Name AS FinanceTypeName, f.PaymentDate, p.Name FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id LEFT JOIN Pastures p ON f.PastureId = p.Id WHERE ft.Name IN ('Income') AND PastureId IS NOT NULL AND fd.UserId = user_id GROUP BY f.PastureId, ft.Name, f.PaymentDate, p.Name;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total expenses incurred by each user."},{"role":"assistant","content":"SELECT fd.UserId, SUM(f.Amount) AS TotalExpenses FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE ft.Name IN ('Expense') GROUP BY fd.UserId;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the monthly expenses for a specific user."},{"role":"assistant","content":"SELECT MONTH(f.PaymentDate) AS MONTH, SUM(f.Amount) AS TotalSpent FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE ft.Name IN ('Expense') AND fd.UserId = user_id GROUP BY MONTH(f.PaymentDate);"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the monthly income for a specific user."},{"role":"assistant","content":"SELECT MONTH(f.PaymentDate) AS Month, SUM(f.Amount) AS TotalEarned FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE ft.Name IN ('Income') AND fd.UserId = user_id GROUP BY MONTH(f.PaymentDate);"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the recent transactions (expenses and income) for a specific user."},{"role":"assistant","content":"SELECT f.Amount, ft.Name AS FinanceTypeName, f.PaymentDate FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id WHERE fd.UserId = user_id ORDER BY f.PaymentDate DESC LIMIT 10;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total expenses grouped by pasture name, filtered by user ID."},{"role":"assistant","content":"SELECT p.Name AS PastureName, SUM(f.Amount) AS TotalExpenses FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id JOIN Pastures p ON f.PastureId = p.Id WHERE ft.Name = 'Expense' AND fd.UserId = user_id GROUP BY p.Name;"}]}
{"messages":[{"role":"user","content":"Write an SQL query to retrieve the total income grouped by pasture name, filtered by user ID."},{"role":"assistant","content":"SELECT p.Name AS PastureName, SUM(f.Amount) AS TotalIncome FROM Finances f JOIN FinanceDetails fd ON f.FinanceDetailId = fd.Id JOIN FinanceTypes ft ON fd.FinanceTypeId = ft.Id JOIN Pastures p ON f.PastureId = p.Id WHERE ft.Name = 'Income' AND fd.UserId = user_id GROUP BY p.Name;"}]}
{"messages":[{"role":"user","content":"How many workers are associated within my ranch."},{"role":"assistant","content":"SELECT COUNT(u.Id) AS WorkerCount FROM RanchUsers ru JOIN AspNetUsers u ON ru.UserId = u.Id JOIN AspNetRoles r ON ru.RoleId = r.Id JOIN Ranches rn ON ru.RanchId = rn.Id WHERE r.Name = 'Worker' AND ru.UserId = userId_id;"}]}
{"messages":[{"role":"user","content":"What are the UserId, WorkerName, and RanchName for all workers associated with my ranch?"},{"role":"assistant","content":"SELECT u.Id AS UserId, u.FullName AS WorkerName, rn.Name AS RanchName FROM RanchUsers ru JOIN AspNetUsers u ON ru.UserId = u.Id JOIN AspNetRoles r ON ru.RoleId = r.Id JOIN Ranches rn ON ru.RanchId = rn.Id WHERE r.Name = 'Worker' AND ru.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"How many cattle do I currently have?"},{"role":"assistant","content":"SELECT COUNT(c.Id) AS CattleCount FROM Cattle c JOIN Ranches r ON c.RanchId = r.Id JOIN RanchUsers ru ON r.Id = ru.RanchId WHERE ru.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What are the basic details of the cattle I currently own?"},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalIdentifier, l.BirthDate, ag.Name AS Gender, at.Name AS AnimalType, TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) AS AgeInMonths, r.Name AS RanchName, u.FullName AS OwnerName FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes at ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id JOIN RanchUsers ru ON r.Id = ru.RanchId WHERE ru.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What is the breakdown of my herd by breed, showing the number of cattle for each breed?"},{"role":"assistant","content":"SELECT bs.Name AS Breed, COUNT(c.Id) AS CattleCount FROM Cattle c JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id WHERE c.UserId = 72051 GROUP BY bs.Name;"}]}
{"messages":[{"role":"user","content":"Can you provide detailed information about my cattle, including their breed, gender, birthdate, and ranch?"},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalIdentifier, l.BirthDate, ag.Name AS AnimalGender, bs.Name AS BreedStatus, r.Name AS RanchName, u.FullName AS UserName FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id WHERE c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"How many calves were born this month"},{"role":"assistant","content":"SELECT COUNT(c.Id) AS CalfCount FROM Cattle c JOIN LiveStock l ON c.Id = l.Id WHERE l.BirthDate >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND l.BirthDate < DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, '%Y-%m-01') AND l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Give details of the calves born this month"},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalIdentifier, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, r.Name AS RanchName, u.FullName AS UserName FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes at ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id WHERE l.BirthDate >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND l.BirthDate < DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, '%Y-%m-01') AND l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What is the ratio of bulls to cows in my herd?"},{"role":"assistant","content":"SELECT SUM(CASE WHEN ag.Name = 'Bull' THEN 1 ELSE 0 END) AS BullCount, SUM(CASE WHEN ag.Name = 'Cow' THEN 1 ELSE 0 END) AS CowCount, (SUM(CASE WHEN ag.Name = 'Bull' THEN 1 ELSE 0 END) / NULLIF(SUM(CASE WHEN ag.Name = 'Cow' THEN 1 ELSE 0 END), 0)) AS BullToCowRatio FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id WHERE l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What is the total weight of all cattle in my herd?"},{"role":"assistant","content":"SELECT SUM(wm.Weight) AS TotalWeight FROM Cattle c JOIN LiveStock l ON c.Id = l.Id LEFT JOIN ActivityWeightMeasurements wm ON wm.ActivityId = (SELECT ActivityId FROM Activities WHERE CattleId = c.Id ORDER BY MeasurementDate DESC LIMIT 1) WHERE l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"How many active cattle do I have in my herd"},{"role":"assistant","content":"SELECT COUNT(c.Id) AS ActiveCattleCount FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalStatuses s ON l.AnimalStatusId = s.Id WHERE s.Name = 'Active' AND l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What is the average age of the cattle in my herd?"},{"role":"assistant","content":"SELECT AVG(TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE())) AS AverageAgeInMonths FROM Cattle c JOIN LiveStock l ON c.Id = l.Id WHERE l.IsDeleted = 0 AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"What is the average age of cows in my herd, broken down by breed status, for the user"},{"role":"assistant","content":"SELECT AVG(TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE())) AS AverageAgeInMonths, bs.Name AS BreedStatus FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id LEFT JOIN AnimalTypes AT ON at.Id = l.AnimalTypeId WHERE l.IsDeleted = 0 AND at.Name = 'Cow' AND c.UserId = user_id GROUP BY bs.Name;"}]}
{"messages":[{"role":"user","content":"What is the average age of cows in my herd, broken down by breed status, for my user"},{"role":"assistant","content":"SELECT AVG(TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE())) AS AverageAgeInMonths, bs.Name AS BreedStatus FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id LEFT JOIN AnimalTypes AT ON at.Id = l.AnimalTypeId WHERE l.IsDeleted = 0 AND at.Name = 'Cow' AND c.UserId = user_id GROUP BY bs.Name;"}]}
{"messages":[{"role":"user","content":"List all my cattle that are due for pregnancy checks."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalIdentifier, l.BirthDate, bs.Name AS BreedStatus, TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) AS AgeInMonths, r.Name AS RanchName, u.FullName AS UserName, ap.CheckDate FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id LEFT JOIN Activities act ON act.CattleId = c.Id LEFT JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Pregnancy Check') LEFT JOIN ActivityPregnancyChecks ap ON ap.ActivityId = act.Id WHERE TIMESTAMPDIFF(MONTH, l.BirthDate, CURDATE()) >= 3 AND l.IsDeleted = 0 AND ap.CheckDate IS NULL AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"How many pregnant cows do I have?"},{"role":"assistant","content":"SELECT COUNT(c.Id) AS PregnantCowCount FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN BreedStatuses bs ON c.BreedStatusId = bs.Id WHERE l.IsDeleted = 0 AND bs.Name = 'Pregnant' AND c.UserId = user_id;"}]}
{"messages":[{"role":"user","content":"Write a SQL query to find all animals on which castration was performed in the current week."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, r.Name AS RanchName, u.FullName AS UserName, ac.CastrationDate, ct.Name AS CastrationTypeName FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes at ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id JOIN Activities act ON act.CattleId = c.Id JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Castration') JOIN ActivityCastrations ac ON ac.ActivityId = act.Id JOIN ActivityCastrationTypes ct ON c.CastrationTypeId = ct.Id WHERE l.IsDeleted = 0 AND u.id = user_id AND ac.CastrationDate >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY AND ac.CastrationDate < CURDATE() + INTERVAL (7 - WEEKDAY(CURDATE())) DAY;"}]}
{"messages":[{"role":"user","content":" Retrieve the details of cattle where the user ID matches a specific user and the castration type is specified."},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, r.Name AS RanchName, u.FullName AS UserName, ac.CastrationDate, ct.Name AS CastrationTypeName FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes AT ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id JOIN Activities act ON act.CattleId = c.Id JOIN ActivityBatches ab ON ab.Id = act.ActivityBatchId AND ab.ActivityTypeId = (SELECT Id FROM ActivityTypes WHERE NAME = 'Castration') JOIN ActivityCastrations ac ON ac.ActivityId = act.Id JOIN ActivityCastrationTypes ct ON c.CastrationTypeId = ct.Id WHERE u.Id = user_id AND l.IsDeleted = 0 AND ct.Name = castration_Type;"}]}
{"messages":[{"role":"user","content":"Retrieve the details of cattle where the sire's visual ID matches a specified visual_id"},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, r.Name AS RanchName, u.FullName AS UserName, c.SireId FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes AT ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id WHERE c.SireId IN (SELECT s.Id FROM Cattle s WHERE s.VisualId = visual_id) AND c.UserId = user_id AND l.IsDeleted = 0;"}]}
{"messages":[{"role":"user","content":"Retrieve the details of cattle where the dam's visual ID matches a specified visual_id"},{"role":"assistant","content":"SELECT c.Id AS CattleId, c.VisualId AS AnimalName, l.BirthDate, ag.Name AS AnimalGender, at.Name AS AnimalType, r.Name AS RanchName, u.FullName AS UserName, c.DamId FROM Cattle c JOIN LiveStock l ON c.Id = l.Id JOIN AnimalGenders ag ON l.AnimalGenderId = ag.Id JOIN AnimalTypes AT ON l.AnimalTypeId = at.Id JOIN Ranches r ON c.RanchId = r.Id JOIN AspNetUsers u ON c.UserId = u.Id WHERE c.DamId IN (SELECT d.Id FROM Cattle d WHERE d.VisualId = visual_id) AND c.UserId = user_id AND l.IsDeleted = 0;"}]}