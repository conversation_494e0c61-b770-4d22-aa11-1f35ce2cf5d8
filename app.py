# app.py
import json
from openai_helper import genai_function_calling, generate_sql_query, generate_agent_response, \
    generate_sql_query_fine_tune, generate_json_response, generate_tutorial_recommendation
from db import execute_sql_query
from flask import Flask, request, jsonify, Response, redirect, url_for
from flasgger import Swagger, swag_from
from embeddings_helper import get_answer, update_embeddings
import os
from werkzeug.utils import secure_filename
import logging
import Constants
import traceback
import hashlib
from flask_cors import CORS
from dotenv import load_dotenv
from claude_helper import claude_generate_sql_query

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
app = Flask(__name__)
CORS(app)
load_dotenv()

# Set up username and password for authentication
USERNAME = os.getenv("SWAGGER_USERNAME")
PASSWORD = os.getenv("SWAGGER_PASSWORD")


# Basic authentication check
def check_auth(username, password):
    """Verify username and password."""
    return username == USERNAME and password == PASSWORD


# Send a 401 response to trigger browser's authentication dialog
def authenticate():
    """Sends a 401 response to enable basic auth."""
    return Response(
        'Login required. Please provide valid credentials.', 401,
        {'WWW-Authenticate': 'Basic realm="Login Required"'}
    )


@app.before_request
def log_request_info():
    logging.info("Headers: %s", request.headers)


# Authentication middleware
@app.before_request
def require_auth():
    # Only protect the root route ('/') and Swagger spec route
    if request.path in ['/', '/apispec_1.json']:
        auth = request.authorization
        if not auth or not check_auth(auth.username, auth.password):
            return authenticate()


# Swagger configuration
swagger = Swagger(app, config={
    'headers': [],
    'specs': [
        {
            'endpoint': 'apispec_1',
            'route': '/apispec_1.json',
            'rule_filter': lambda rule: True,  # Include all endpoints
            'model_filter': lambda tag: True,  # Include all models
        }
    ],
    'static_url_path': "/flasgger_static",
    'swagger_ui': True,
    'specs_route': "/",
    'title': "Cattlytics Gen AI ",
    'version': "1.0.0",  # API version
    'description': "API documentation for the Cattlytics platform. This API allows you to manage livestock data, advance analytics, and query knowledge base resources.",
    'termsOfService': "",
    'contact': {
        'name': "Cattlytics Support",
        'url': "https://cattlytics.com",
        'email': "<EMAIL>"
    },
    'license': {
        'name': "Apache 2.0",
        'url': "https://www.apache.org/licenses/LICENSE-2.0.html"
    },
    'securityDefinitions': {
        'ApiKeyAuth': {
            'type': 'apiKey',
            'name': 'X-API-KEY',
            'in': 'header'
        }
    },
    'security': [{'ApiKeyAuth': []}]  # Add this line to apply security globally
})


# Root route serving Swagger UI
@app.route('/')
def swagger_root():
    return redirect(url_for('flasgger.apispec_1'))


# Set the directory where uploaded files will be saved
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Allowed extensions for file uploads
ALLOWED_EXTENSIONS = {'docx'}
# Load and hash API keys from the .env file
raw_api_keys = os.getenv("API_KEYS", "").split(",")
valid_api_keys = {hashlib.sha256(key.strip().encode()).hexdigest() for key in raw_api_keys if key.strip()}


# Function to check if file extension is allowed
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def get_project_path(name):
    return os.path.join(app.config['UPLOAD_FOLDER'], name)


def get_embedding_path(name):
    return os.path.join(name, "embeddings")


def require_api_key(f):
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get("X-API-KEY")
        logging.info("api_key: %s", api_key)
        hashed_key = hashlib.sha256(api_key.encode()).hexdigest() if api_key else None
        if hashed_key not in valid_api_keys:  # Remove `.values()` here
            return jsonify({"error": "Need Valid API Key"}), 401
        return f(*args, **kwargs)

    decorated_function.__name__ = f.__name__
    return decorated_function


@app.route('/embeddings/create', methods=['POST'])
@swag_from({
    'tags': ['Knowledge Base'],
    'security': [{'ApiKeyAuth': []}],
    'description': 'Process user documents in the specified folder and update embeddings.',
    'parameters': [
        {
            'name': 'project_name',
            'type': 'string',
            'in': 'formData',
            'required': True,
            'description': 'The name of the project for which the embeddings are being created.'
        },
        {
            "name": "file",
            "in": "formData",
            "type": "file",
            "required": True,
            "description": "Upload a DOCX file to generate embeddings"
        }
    ],
    'responses': {
        200: {
            'description': 'Successfully processed documents and updated embeddings.',
            'schema': {
                'type': 'object',
                'properties': {
                    'response': {
                        'type': 'array',
                        'items': {
                            'type': 'string',
                            'example': 'documents/Cattlytics Overview.docx'
                        },
                        'description': 'List of processed document paths'
                    }
                }
            }
        },
        400: {
            'description': 'Error occurred during document processing.',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {
                        'type': 'string',
                        'example': 'Error message describing the issue.'
                    }
                }
            }
        }
    }
})
@require_api_key
def embeddings_create():
    try:
        # Check if the 'file' part is in the request
        if 'file' not in request.files:
            return jsonify({"message": "No file part"}), 400

        file = request.files['file']

        # If no file is selected
        if file.filename == '':
            return jsonify({"message": "No selected file"}), 400

        project_name = request.form.get('project_name')
        base_path = get_project_path(project_name)

        if not os.path.exists(base_path):
            os.makedirs(base_path)
            embedding_path = get_embedding_path(base_path)
            os.makedirs(embedding_path)
            logging.info("embedding_path  %s", embedding_path)
            print(f"Folder created: {base_path}")
        else:
            print(f"Folder already exists: {base_path}")

        # Validate file extension
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(base_path, filename)
            file.save(filepath)
            logging.info("filename: %s", filename)
            logging.info("filepath: %s", filepath)

            documents = []
            for filename in os.listdir(base_path):
                if filename.endswith(".docx"):
                    documents.append(os.path.join(base_path, filename))
                    update_embeddings(os.path.join(base_path, filename), embedding_path)

            return jsonify({
                "response": documents
            })

        else:
            return jsonify({"message": "Invalid file type. Only DOCX files are allowed."}), 400
    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500


@app.route('/embeddings/knowledge-base', methods=['POST'])
@swag_from({
    'tags': ['Knowledge Base'],
    'security': [{'ApiKeyAuth': []}],
    'parameters': [
        {
            'name': 'project_name',
            'type': 'string',
            'in': 'formData',
            'required': True,
            'description': 'The name of the project for which the embeddings are being created.'
        },
        {
            'name': 'question',
            'in': 'formData',
            'type': 'string',
            'required': True,
            'description': 'The query from the user.'
        }
    ],
    'responses': {
        200: {
            'description': 'The generated SQL query result',
            'schema': {
                'type': 'object',
                'properties': {
                    'answer': {
                        'type': 'string'
                    },
                    'relevant_context': {
                        'type': 'string'
                    },
                    'confidence_score': {
                        'type': 'string'
                    }
                }
            }
        }
    }
})
@require_api_key
def embeddings_knowledge_base():
    # Get the data from form inputs instead of JSON
    question = request.form.get('question', '')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    project_name = request.form.get('project_name')

    try:
        base_path = get_project_path(project_name)
        embedding_path = get_embedding_path(base_path)
        # Generate human-readable response
        answer, relevant_context, confidence_score = get_answer(question, "gpt-4o-mini", embedding_path)

        return jsonify({
            "answer": answer,
            "relevant_context": relevant_context,
            "confidence_score": str(confidence_score),
        })

    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500


@app.route('/sql-agent/query', methods=['POST'])
@swag_from({
    "tags": ["SQL Queries"],
    'security': [{'ApiKeyAuth': []}],
    "parameters": [
        {
            "name": "question",
            "in": "formData",
            "type": "string",
            "required": True,
            "description": "The query from the user."
        },
        {
            "name": "userId",
            "in": "formData",
            "type": "integer",
            "required": True,
            "description": "The user ID."
        }
    ],
    "responses": {
        200: {
            "description": "The generated SQL query result",
            "schema": {
                "type": "object",
                "properties": {
                    "sql_query": {"type": "string"},
                    "results": {
                        "type": "array",
                        "items": {"type": "string"},
                        "example": ["Result 1", "Result 2"]
                    },
                    "agent_response": {"type": "string"}
                }
            }
        }
    }
})
@require_api_key
def sql_agent_query():
    # Get the data from form inputs instead of JSON
    user_query = request.form.get('question', '')
    user_id = request.form.get('userId', '')

    if not user_query:
        return jsonify({"error": "No question provided"}), 400
    if not user_id:
        return jsonify({"error": "No userId provided"}), 400
    # Generate SQL query

    try:
        sql_query = generate_sql_query(user_query, user_id, "gpt-4o-mini")

        # Execute SQL query
        results = execute_sql_query(sql_query)
        # Generate human-readable response
        agent_response = generate_agent_response(user_query, sql_query, results, "gpt-4o-mini")

        return jsonify({
            "sql_query": sql_query,
            "results": results,
            "agent_response": agent_response
        })

    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500


@app.route('/sql-agent/claude-query', methods=['POST'])
@swag_from({
    "tags": ["SQL Queries"],
    'security': [{'ApiKeyAuth': []}],
    "parameters": [
        {
            "name": "question",
            "in": "formData",
            "type": "string",
            "required": True,
            "description": "The query from the user."
        },
        {
            "name": "userId",
            "in": "formData",
            "type": "integer",
            "required": True,
            "description": "The user ID."
        }
    ],
    "responses": {
        200: {
            "description": "The generated SQL query result",
            "schema": {
                "type": "object",
                "properties": {
                    "sql_query": {"type": "string"},
                    "results": {
                        "type": "array",
                        "items": {"type": "string"},
                        "example": ["Result 1", "Result 2"]
                    },
                    "agent_response": {"type": "string"}
                }
            }
        }
    }
})
@require_api_key
def sql_agent_claude_query():
    user_query = request.form.get('question', '')
    user_id = request.form.get('userId', '')

    if not user_query:
        return jsonify({"error": "No question provided"}), 400
    if not user_id:
        return jsonify({"error": "No userId provided"}), 400

    try:
        # Generate SQL query
        sql_query = claude_generate_sql_query(user_query, user_id)
        logging.info(f"Generated SQL query: {sql_query}")
        # Execute SQL query
        results = execute_sql_query(sql_query)
        logging.info(f"SQL query results: {results}")
        # Generate human-readable response
        agent_response = generate_agent_response(user_query, sql_query, results, "gpt-4o-mini")
        logging.info(f"Generated human-readable response: {agent_response}")
    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500

    return jsonify({
        "sql_query": sql_query,
        "results": results,
        "agent_response": agent_response
    })


@app.route('/sql-agent/fine-tune', methods=['POST'])
@swag_from({
    'tags': ['SQL Queries'],
    'security': [{'ApiKeyAuth': []}],
    'parameters': [
        {
            'name': 'question',
            'in': 'formData',
            'type': 'string',
            'required': True,
            'description': 'The query from the user.'
        },
        {
            'name': 'userId',
            'in': 'formData',
            'type': 'integer',
            'required': True,
            'description': 'The user ID.'
        }
    ],
    'responses': {
        200: {
            'description': 'The generated SQL query result',
            'schema': {
                'type': 'object',
                'properties': {
                    'sql_query': {
                        'type': 'string'
                    },
                    'results': {
                        'type': 'array',
                        'items': {
                            'type': 'string'
                        },
                        'example': ["Result 1", "Result 2"]
                    },
                    'agent_response': {
                        'type': 'string'
                    }
                }
            }
        }
    }
})
@require_api_key
def sql_agent_fine_tune_query():
    # Get the data from form inputs instead of JSON
    user_query = request.form.get('question', '')
    user_id = request.form.get('userId', '')

    if not user_query:
        return jsonify({"error": "No question provided"}), 400
    if not user_id:
        return jsonify({"error": "No userId provided"}), 400
    # Generate SQL query
    try:

        sql_query = generate_sql_query_fine_tune(user_query, user_id,
                                                 "ft:gpt-4o-mini-2024-07-18:folio3-agtech::AMFQRURs")

        # Execute SQL query
        results = execute_sql_query(sql_query)
        # Generate human-readable response
        agent_response = generate_agent_response(user_query, sql_query, results, "gpt-4o-mini")

        return jsonify({
            "sql_query": sql_query,
            "results": results,
            "agent_response": agent_response
        })
    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500


@app.route('/sql-agent/function-call', methods=['POST'])
@swag_from({
    "tags": ["SQL Queries"],
    'security': [{'ApiKeyAuth': []}],
    "parameters": [
        {
            "name": "question",
            "in": "formData",
            "type": "string",
            "required": True,
            "description": "The query from the user."
        },
        {
            "name": "userId",
            "in": "formData",
            "type": "integer",
            "required": True,
            "description": "The user ID."
        }
    ],
    "responses": {
        200: {
            "description": "The generated SQL query result",
            "schema": {
                "type": "object",
                "properties": {
                    "sql_query": {"type": "string"},
                    "results": {
                        "type": "array",
                        "items": {"type": "string"},
                        "example": ["Result 1", "Result 2"]
                    },
                    "agent_response": {"type": "string"}
                }
            }
        }
    }
})
@require_api_key
def sql_agent_function_call():
    # Get the data from form inputs instead of JSON
    user_query = request.form.get('question', '')
    user_id = request.form.get('userId', '')

    if not user_query:
        return jsonify({"error": "No question provided"}), 400
    if not user_id:
        return jsonify({"error": "No userId provided"}), 400
    # Generate SQL query
    try:

        response = genai_function_calling(user_query, user_id)

        return jsonify(response)
    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500

@app.route('/action-agent/generate-requets-payload', methods=['POST'])
@swag_from({
    'tags': ['Action Agent'],
    'security': [{'ApiKeyAuth': []}],
    'description': 'Generates a JSON payload based on the user prompt.',
    'parameters': [
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                'type': 'object',
                'properties': {
                    'entity': {
                        'type': 'string',
                        'example': 'Pasture'
                    },
                    'chatId': {
                        'type': 'string',
                        'example': 'cb719289-9d6c-4e07-a0ef-8960fc41c2a5'
                    },
                    'prompt': {
                        'type': 'string',
                        'example': 'Add a new pasture named Green Valley with 30 acres. I purchased it from John Doe for 100$.'
                    }
                },
                'required': ['prompt']
            }
        }
    ],
    'responses': {
        '200': {
            'description': 'Generated JSON payload based on user prompt',
            'schema': {
                'type': 'object',
                'example': {
                    "action": "add_pasture",
                    "name": "Green Valley",
                    "location": "Default Location",
                    "size": 30
                }
            }
        },
        '400': {
            'description': 'Prompt is required',
            'schema': {
                'type': 'object',
                'example': {
                    "error": "Prompt is required"
                }
            }
        },
        '500': {
            'description': 'Failed to parse JSON response',
            'schema': {
                'type': 'object',
                'example': {
                    "error": "Failed to parse JSON response"
                }
            }
        }
    }
})
@require_api_key
def action_agent_generate_requets_payload():
    data = request.json
    
    entity = data.get('entity')
    user_prompt = data.get('prompt')
    chatId = data.get('chatId')
    
    userId = data.get('userId')
    
    if not user_prompt or not entity or not chatId:
        return jsonify({"error": {"message": "Input is required"}}), 400
    
    if entity not in Constants.ALLOWED_ENTITIES:
        return jsonify({"error": "Invalid entity type"}), 400
    
    gpt_response = generate_json_response(chatId, entity, user_prompt, userId)

    print("Here")
    return jsonify(gpt_response)

@app.route('/tutorials/search', methods=['POST'])
@swag_from({
    'tags': ['Tutorials'],
    'security': [{'ApiKeyAuth': []}],
    'description': 'Search for relevant Cattlytics tutorial videos based on user prompt.',
    'parameters': [
        {
            'name': 'prompt',
            'in': 'formData',
            'type': 'string',
            'required': True,
            'description': 'The user prompt for searching tutorials.',
            'example': 'How do I add a new cattle to my inventory?'
        },
        {
            'name': 'chatId',
            'in': 'formData',
            'type': 'string',
            'required': True,
            'description': 'Chat session ID.',
            'example': 'cb719289-9d6c-4e07-a0ef-8960fc41c4r5'
        },
        {
            'name': 'userId',
            'in': 'formData',
            'type': 'string',
            'required': True,
            'description': 'User ID.',
            'example': '10000'
        }
    ],
    'responses': {
        '200': {
            'description': 'Relevant tutorial videos found',
            'schema': {
                'type': 'object',
                'properties': {
                    'response': {
                        'type': 'string',
                        'description': 'A natural language response explaining the recommended videos'
                    },
                    'videos': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'url': {'type': 'string'},
                                'title': {'type': 'string'}
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid input',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    }
})
@require_api_key
def search_tutorials():
    prompt = request.form.get('prompt')
    chatId = request.form.get('chatId')
    userId = request.form.get('userId')

    if not prompt:
        return jsonify({"error": "Prompt is required"}), 400

    try:
        result = generate_tutorial_recommendation(chatId, userId, prompt)
        return jsonify(result)

    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logging.error(f"Error: {error_message}\n{stack_trace}")
        return jsonify({
            "error": "Failed to process the request",
            "message": error_message,
            "stack_trace": stack_trace
        }), 500

if __name__ == '__main__':
    app.run(port=8000, debug=True)
