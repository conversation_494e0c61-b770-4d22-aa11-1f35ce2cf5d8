import openai
import faiss
import numpy as np
from docx import Document
from dotenv import load_dotenv
import os
import logging
import json

from config import OPENAI_API_KEY

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

load_dotenv()

# Set up your OpenAI API key
openai.api_key = OPENAI_API_KEY
embeddingsPath = "embeddings.npy"
indexPath = "faiss.index"
sectionsPath = "sections.json"  # New path for saving text sections
max_tokens = 500


# Helper function to split text into smaller chunks
def split_text(text, max_tokens=max_tokens):
    """
    Splits text into chunks of a specified maximum token length.
    """
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(current_chunk) >= max_tokens:
            chunks.append(" ".join(current_chunk))
            current_chunk = []

    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(" ".join(current_chunk))

    return chunks


# 1. Extract text from a Word document
def extract_text_from_docx(file_path):
    doc = Document(file_path)
    return "\n".join([para.text for para in doc.paragraphs if para.text])


# 2. Generate embeddings for the extracted text
def generate_embeddings(text):
    response = openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding


# 3. Set up FAISS index to store embeddings
def create_faiss_index(dimension):
    index = faiss.IndexFlatL2(dimension)
    return index


# 4. Save embeddings, FAISS index, and document sections to file
def save_embeddings_and_index(embeddings, index, sections, embeddings_file, index_file,sections_file):
    np.save(embeddings_file, embeddings)  # Save embeddings as a numpy array
    faiss.write_index(index, index_file)  # Save the FAISS index
    with open(sections_file, "w") as f:
        json.dump(sections, f)  # Save the document sections (actual text) to a JSON file


# 5. Load embeddings, FAISS index, and document sections from file
def load_embeddings_and_index(embeddings_file, index_file, sections_file):
    embeddings = np.load(embeddings_file)
    index = faiss.read_index(index_file)
    with open(sections_file, "r") as f:
        sections = json.load(f)
    return embeddings, index, sections


# 6. Handle user queries by generating embedding for the query and retrieving closest match from FAISS index
def query_embeddings(index, query, k=3, threshold=0.5):
    query_embedding = generate_embeddings(query)
    query_vector = np.array([query_embedding]).astype('float32')
    D, I = index.search(query_vector, k=k)  # Retrieve the top k matches

    # Filter results based on the threshold
    matches = [(I[0][i], D[0][i]) for i in range(k) if D[0][i] < threshold]

    if matches:
        return matches  # Return a list of tuples (index, distance)
    else:
        return None  # Return None if no close matches are found


# 7. Use GPT to generate a response based on the relevant context
def gpt_answer(question, context, model):
    logging.info("Context: %s", context)
    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system",
             "content": "You are a helpful cattlytics assistant which help user to answer their issues using the context you have."},
            {"role": "user", "content": f"{context}\n\n{question}"}
        ]
    )
    return response.choices[0].message.content


# 8. Method to update embeddings and index
# 8. Method to update embeddings and index (modified for appending)
def update_embeddings(docx_file, embedding_path):

    embeddings_file = os.path.join(embedding_path, embeddingsPath)
    index_file = os.path.join(embedding_path, indexPath)
    sections_file = os.path.join(embedding_path, sectionsPath)

    # Step 1: Extract text from the document
    document_text = extract_text_from_docx(docx_file)

    # Step 2: Split text into smaller sections for embedding
    document_sections = split_text(document_text)

    # Step 3: Generate embeddings for each section
    document_embeddings = [generate_embeddings(section) for section in document_sections]

    # Step 4: Load existing embeddings, FAISS index, and document sections (if they exist)
    if os.path.exists(embeddings_file) and os.path.exists(index_file) and os.path.exists(sections_file):
        # Load existing data
        existing_embeddings, faiss_index, existing_sections = load_embeddings_and_index(embeddings_file, index_file,
                                                                                        sections_file)

        # Step 5: Append the new embeddings to the existing ones
        document_embeddings = existing_embeddings.tolist() + document_embeddings
        document_sections = existing_sections + document_sections

        # Convert to float32 numpy array for FAISS
        vectors = np.array(document_embeddings).astype('float32')

        # Update the FAISS index with the new embeddings
        faiss_index.add(np.array(document_embeddings[len(existing_embeddings):]).astype('float32'))

    else:
        # If no previous data, create a new FAISS index
        dimension = len(document_embeddings[0])
        faiss_index = create_faiss_index(dimension)

        # Convert to float32 numpy array for FAISS
        vectors = np.array(document_embeddings).astype('float32')

        # Add embeddings to the FAISS index
        faiss_index.add(vectors)

    # Step 6: Save the updated embeddings, FAISS index, and document sections
    save_embeddings_and_index(vectors, faiss_index, document_sections, embeddings_file, index_file, sections_file)
    print(f"Embeddings and index updated successfully for document: {docx_file}.")


# 9. Method to get an answer using saved embeddings and index
def get_answer(question, model,embedding_path):
    # Check if embeddings, FAISS index, and document sections are already saved
    embeddings_file = os.path.join(embedding_path, embeddingsPath)
    index_file = os.path.join(embedding_path, indexPath)
    sections_file = os.path.join(embedding_path, sectionsPath)

    if not os.path.exists(embeddings_file) or not os.path.exists(index_file) or not os.path.exists(sections_file):
        print("Embeddings, index, or document sections not found. Please update them first.")
        return None, None, None

    # Load embeddings, FAISS index, and document sections from saved files
    vectors, faiss_index, document_sections = load_embeddings_and_index(embeddings_file, index_file, sections_file)

    # Step 6: Retrieve the most relevant document sections based on the query
    matches = query_embeddings(faiss_index, question)

    if matches is None:
        # If no close match is found, provide a fallback response
        print("No relevant context found for the question.")
        return "Sorry, I couldn't find any relevant information in the provided context.", None, None

    # Merge relevant contexts based on the indices returned
    relevant_contexts = [document_sections[idx] for idx, _ in matches]  # Get the text for each relevant index
    merged_context = " ".join(relevant_contexts)  # Concatenate contexts

    logging.info("Merged context: %s", merged_context)

    # Step 7: Use GPT to generate an answer based on the merged context
    answer = gpt_answer(question, merged_context, model)

    # Display the result
    print(f"Answer: {answer}")

    # Optionally return the confidence score with the answer
    # Calculate average confidence score from the distances of matched results
    confidence_score = sum(1 / (1 + distance) for _, distance in matches) / len(matches)  # Average score
    return answer, merged_context, confidence_score
