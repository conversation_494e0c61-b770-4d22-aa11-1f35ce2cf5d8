# Cattlytics Query API

This is a Flask-based API that allows users to generate and execute SQL `SELECT` queries based on user input. The API connects to a MySQL database, utilizes OpenAI for generating SQL queries, and provides Swagger documentation for easy interaction.

# Cattlytics Knowledge Base Chatbot

This project implements a chatbot that answers questions based on a set of knowledge base documents using OpenAI's GPT models. The chatbot uses document embeddings and a FAISS index to search relevant parts of the document and generate responses.


## Features
- Generate SQL queries using OpenAI based on user input.
- Execute queries against a MySQL database.
- Use Flask to expose an API for interacting with the system.
- Swagger (Flasgger) integration for easy testing and documentation of the API.
- Extract text from Word documents (`.docx`)
- Generate document embeddings using OpenAI's `text-embedding-ada-002`
- Store and search embeddings using FAISS (a vector search engine)
- Query the knowledge base with natural language
- Use OpenAI's GPT model to generate responses based on the relevant document sections

---

## Prerequisites
- Python 3.x
- MySQL Server
- OpenAI API Key
- Word documents (.docx) for the knowledge base


## Set Virtual Env
- `python -m venv Cattlytics`
- `.\Cattlytics\Scripts\Activate`
- `pip install openai mysql-connector-python`
- `pip install Flask`
- `pip install Flask flasgger`
- `pip install python-dotenv`
- `pip install gunicorn`
- `pip install anthropic`
- `pip install faiss-cpu python-docx numpy`


or using conda
- `conda create --name cattlytics-ai`
- `conda activate cattlytics-ai`
- `conda install openai`
- `conda install mysql-connector-python`
- `conda install flask`
- `conda install flasgger`
- `conda install python-dotenv`

## Setup
- Create a `.env` file in the root directory with the following variables:
  ```
  OPENAI_API_KEY=<your_openai_api_key>
  MYSQL_HOST=<your_mysql_host>
  MYSQL_USER=<your_mysql_user>
  MYSQL_PASSWORD=<your_mysql_password>
  MYSQL_DATABASE=<your_mysql_database>
  ```

## Set Virtual Env
- `python -m venv cattlytics-ai`
- `.\cattlytics-ai\Scripts\Activate`




## Run
- `python app.py`
- Open `http://127.0.0.1:8000` in your browser to interact with the API.

## Browser Authentication
- `User:CattlyticsAI`
- `Password:3e0jOGMp2y4M`

## Protected APIs 
- Define `API_KEYS` in .env file
- Without valid API key APIs will not work