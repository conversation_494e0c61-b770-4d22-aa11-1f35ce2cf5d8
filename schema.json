{"AnimalTypes": {"description": "Contains types of animals like Bull, Cow, Calf.", "columns": {"Name": "Name of the animal type.", "Description": "Additional details about the animal type."}, "foreign_keys": {}}, "AnimalGenders": {"description": "Contains genders of animals like <PERSON>, <PERSON><PERSON>, <PERSON>eer.", "columns": {"Name": "Name of the gender type.", "Description": "Additional details about the gender."}, "foreign_keys": {}}, "AnimalStatuses": {"description": "Contains statuses of animals such as Active, Dead, Sold, Reference.", "columns": {"Name": "Name of the status.", "Description": "Additional details about the status."}, "foreign_keys": {}}, "BreedStatuses": {"description": "Contains breeding statuses like Open, Exposed, Pregnant.", "columns": {"Name": "Name of the breeding status.", "Description": "Additional details about the breeding status."}, "foreign_keys": {}}, "Breeds": {"description": "Contains information on animal breeds.", "columns": {"Name": "Name of the breed.", "Description": "Additional details about the breed."}, "foreign_keys": {}}, "PastureFrequencies": {"description": "Specifies the frequency of pasture use, like Monthly or Yearly.", "columns": {"Name": "Frequency name.", "Description": "Additional details about the frequency."}, "foreign_keys": {}}, "PastureOwnerships": {"description": "Specifies the ownership type of pastures, such as Owned, Rented, Leased.", "columns": {"Name": "Type of ownership.", "Description": "Additional details about ownership."}, "foreign_keys": {}}, "Pastures": {"description": "Contains details about pastures, including area, name, and ownership.", "columns": {"UserId": "ID of the user managing the pasture.", "Area": "Area of the pasture.", "Name": "Name of the pasture.", "Description": "Description of the pasture.", "AreaUnitId": "ID referencing units for area.", "Cost": "Cost associated with the pasture.", "PastureFrequencyId": "ID linking to frequency of pasture use.", "PastureOwnershipId": "ID linking to pasture ownership type.", "PaymentDate": "Date of payment related to the pasture."}, "foreign_keys": {"UserId": "AspNetUsers.Id", "PastureFrequencyId": "PastureFrequencies.Id", "PastureOwnershipId": "PastureOwnerships.Id", "AreaUnitId": "Units.Id"}}, "Animals": {"description": "Contains details about individual animals, including breed, type, gender, and status.", "columns": {"AnimalTypeId": "ID linking to animal type.", "AnimalGenderId": "ID linking to animal gender.", "AnimalStatusId": "ID linking to animal status.", "BirthDate": "<PERSON>'s birth date.", "BirthWeight": "Birth weight of the animal.", "AnimalOwnershipTypeId": "ID linking to ownership type.", "Weight": "Current weight of the animal.", "SalePrice": "Sale price of the animal.", "DeathCauseDate": "Date when the animal died.", "OwnersName": "Name of the owner.", "PurchaseDate": "Date of purchase.", "PurchasePrice": "Purchase price of the animal.", "ColorId": "ID linking to color.", "VisualId": "Visual identification number.", "EID": "Electronic ID of the animal.", "PastureId": "ID linking to pasture.", "BreedId": "ID linking to breed.", "BreedStatusId": "ID linking to breed status.", "AnimalConceptionTypeId": "ID linking to conception type.", "SireId": "ID linking to sire (parent).", "DamId": "ID linking to dam (parent).", "WeaningDate": "Date of weaning.", "WeaningWeight": "Weight at weaning.", "YearlingDate": "Yearling date.", "YearlingWeight": "Weight at yearling.", "UserId": "ID of the user managing the animal.", "CastrationDate": "Date of castration.", "CastrationTypeId": "ID linking to castration type.", "WeaningStatusId": "ID linking to weaning status.", "BreedingDate": "Breeding date.", "IsLock": "Indicates if the animal is locked for sale.", "PriceUnitId": "ID linking to unit for price.", "UnitId": "ID linking to unit."}, "foreign_keys": {"AnimalTypeId": "AnimalTypes.Id", "AnimalGenderId": "AnimalGenders.Id", "AnimalStatusId": "AnimalStatuses.Id", "AnimalOwnershipTypeId": "AnimalOwnershipTypes.Id", "ColorId": "Units.Id", "CastrationTypeId": "ActivityCastrationTypes.Id", "AnimalConceptionTypeId": "AnimalConceptionTypes.Id", "UserId": "AspNetUsers.Id", "BreedId": "Breeds.Id", "BreedStatusId": "BreedStatuses.Id", "DamId": "Animals.Id", "SireId": "Animals.Id", "PastureId": "Pastures.Id", "PriceUnitId": "Units.Id", "UnitId": "Units.Id", "WeaningStatusId": "WeaningStatuses.Id"}}, "CattleGroups": {"description": "Contains cattle group details.", "columns": {"UserId": "ID of the user who owns the group.", "Name": "Name of the cattle group."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "FinanceTypes": {"description": "Contains types of finances like Income and Expense.", "columns": {"Name": "Type of finance, such as Income or Expense."}, "foreign_keys": {}}, "FinanceDetails": {"description": "Contains subcategories of finance types.", "columns": {"UserId": "ID of the user.", "FinanceTypeId": "ID linking to finance type.", "Name": "Subcategory name under a finance type."}, "foreign_keys": {"UserId": "AspNetUsers.Id", "FinanceTypeId": "FinanceTypes.Id"}}, "Finances": {"description": "Contains finance records, such as income and expense.", "columns": {"FinanceDetailId": "ID linking to finance detail.", "RecordDate": "Date of record.", "PaymentDate": "Payment date.", "Notes": "Additional notes.", "Amount": "Amount involved in the transaction.", "PriceUnitId": "ID linking to the unit for price.", "AdministratedById": "ID linking to administrator.", "CattleId": "ID linking to cattle involved.", "PastureId": "ID linking to pasture involved."}, "foreign_keys": {"FinanceDetailId": "FinanceDetails.Id", "AdministratedById": "AspNetUsers.Id", "CattleId": "Animals.Id", "PastureId": "Pastures.Id"}}, "ActivityTypes": {"description": "Contains types of activities that can be performed, such as Pasture Movement, New Treatment, and Castration.", "columns": {"Name": "Name of the activity type, e.g., Pasture Movement, Weaning, Calving."}, "foreign_keys": {}}, "ActivitiesPerformed": {"description": "Logs activities performed on cattle, including type, date, and notes about each activity.", "columns": {"UserId": "ID of the user performing the activity.", "ActivityTypeId": "ID linking to the type of activity performed.", "Notes": "Additional notes on the activity.", "ActivityDate": "Date when the activity was performed.", "ActivityBatchId": "ID linking to the batch of activities performed.", "CattleId": "ID linking to the cattle involved in the activity."}, "foreign_keys": {"UserId": "AspNetUsers.Id", "ActivityTypeId": "ActivityTypes.Id", "ActivityBatchId": "ActivityBatches.Id", "CattleId": "Animals.Id"}}, "AspNetRoles": {"description": "Contains roles for users in the system, such as <PERSON><PERSON>, Veterinarian, and Ranch Owner.", "columns": {"Name": "Name of the role, such as <PERSON><PERSON> or <PERSON>.", "NormalizedName": "Normalized version of the role name.", "RoleCategoryId": "ID linking to the category of the role."}, "foreign_keys": {"RoleCategoryId": "applicationenums.Id"}}, "AspNetUsers": {"description": "Contains user details, such as name, address, contact info, and language preference.", "columns": {"FullName": "Full name of the user.", "Address": "Physical address of the user.", "IsActive": "Indicates if the user is active.", "CellNumber": "Cell phone number of the user.", "PhoneNumber": "Phone number of the user.", "LanguageId": "ID linking to the user's preferred language.", "UserName": "Username of the user.", "NormalizedUserName": "Normalized version of the username.", "Email": "Email address of the user.", "NormalizedEmail": "Normalized version of the email address."}, "foreign_keys": {"LanguageId": "languages.Id", "PromoCodeId": "promocodes.Id"}}, "ActivityYearling": {"description": "Records yearling activities, including date and weight of the animal.", "columns": {"YearlingDate": "Date of the yearling activity.", "Weight": "Weight of the animal at yearling.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id"}}, "ActivityWeightMeasurements": {"description": "Logs weight measurement activities, including date and weight of the animal.", "columns": {"MeasurementDate": "Date of the weight measurement activity.", "Weight": "Weight of the animal.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id"}}, "ActivityWeaning": {"description": "Records weaning activities, including date and weight at weaning.", "columns": {"WeaningDate": "Date of the weaning activity.", "Weight": "Weight of the animal at weaning.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id"}}, "ActivityTreatments": {"description": "Logs treatment activities performed on animals, including medication, diagnosis, dosage, and cost.", "columns": {"TreatmentDate": "Date when the treatment was performed.", "TreatmentName": "Name of the treatment administered.", "ActivityMedicationId": "ID linking to the medication used in treatment.", "ActivityDiagnosisId": "ID linking to the diagnosis associated with treatment.", "Dosage": "Dosage given for the treatment.", "ActivityTreatmentRouteId": "ID linking to the route of administration.", "ActivityLocationId": "ID linking to location on animal where treatment was administered.", "ActivityUserId": "ID of the user administering the treatment.", "ActivityId": "ID linking to the main activity record.", "PaymentDate": "Date of payment related to the treatment.", "TreatmentCost": "Cost of the treatment."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityDiagnosisId": "ActivityDiagnoses.Id", "ActivityMedicationId": "ActivityMedications.Id", "ActivityTreatmentRouteId": "ActivityRoutes.Id", "ActivityLocationId": "ActivityTreatmentLocations.Id", "ActivityUserId": "AspNetUsers.Id"}}, "ActivityTreatmentLocations": {"description": "Defines specific locations on the animal where treatments can be applied, such as Left Ear or Lower Neck.", "columns": {"Name": "Location name where treatment can be applied, e.g., Right Flank or Top Line.", "Description": "Additional description of the treatment location."}, "foreign_keys": {}}, "ActivitySemenCollections": {"description": "Records semen collection activities, including volume, concentration, motility, and morphology.", "columns": {"ActivityDate": "Date of the semen collection.", "SemenVolume": "Volume of semen collected.", "SpermConcentration": "Concentration of sperm in the sample.", "MotilityPercentage": "Percentage of motile sperm in the sample.", "MorphologyPercentage": "Percentage of normal morphology in the sample.", "SemenContainerId": "ID linking to the container used for storage.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "SemenContainerId": "semencontainers.Id"}}, "ActivitySales": {"description": "Records sale activities, including date, price, and buyer information.", "columns": {"SaleDate": "Date of the sale.", "SalePrice": "Price at which the animal was sold.", "ActivityId": "ID linking to the main activity record.", "SoldTo": "Information about the buyer.", "PriceUnitId": "ID linking to the unit of price measurement."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "PriceUnitId": "units.Id"}}, "ActivityRoutes": {"description": "Contains administration routes for treatments, such as Intravenous, Oral, and Topical.", "columns": {"Name": "Name of the route of administration, e.g., Intravenous, Topical.", "Description": "Additional description of the administration route."}, "foreign_keys": {}}, "ActivityRecordHeats": {"description": "Records estrus detection activities, including date and detection method.", "columns": {"ActivityDate": "Date when the heat activity was recorded.", "ActivityEstrusDetectionId": "ID linking to the method of estrus detection.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityEstrusDetectionId": "applicationenums.Id"}}, "ActivityPurposes": {"description": "Specifies the purposes for which activities are performed, linked to specific users.", "columns": {"UserId": "ID of the user defining the purpose.", "Name": "Name of the purpose.", "Description": "Additional description of the purpose."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityPromotions": {"description": "Logs promotion activities, including type, date, and details of promotion.", "columns": {"PromotionType": "Type of promotion applied to the animal.", "ActivityId": "ID linking to the main activity record.", "PromotionDate": "Date of the promotion activity."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id"}}, "ActivityPregnancyChecks": {"description": "Records pregnancy check activities, including results, method, and duration.", "columns": {"CheckDate": "Date when the pregnancy check was performed.", "PastureResultId": "ID linking to the result of the pasture check.", "PregnancyCheckMethodId": "ID linking to the method used for pregnancy check.", "Notes": "Additional notes about the check.", "PregnancyDuration": "Duration of the pregnancy recorded.", "ProgesteroneLevel": "Progesterone level if tested.", "BreedStatusId": "ID linking to breeding status.", "AnimalGenderId": "ID linking to gender of the animal.", "BloodTestId": "ID linking to blood test results.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "AnimalGenderId": "AnimalGenders.Id", "BloodTestId": "BloodTests.Id", "BreedStatusId": "BreedStatuses.Id", "PregnancyCheckMethodId": "PregnancyCheckMethods.Id"}}, "ActivityPastureMovements": {"description": "Records pasture movements of animals, including purpose, previous pasture, and user details.", "columns": {"PastureId": "ID linking to the current pasture.", "PreviousPastureId": "ID linking to the previous pasture.", "ActivityPurposeId": "ID linking to the purpose of the movement.", "ActivityUserId": "ID of the user recording the movement.", "MovementDate": "Date of the movement.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityPurposeId": "ActivityPurposes.Id", "ActivityUserId": "AspNetUsers.Id", "PastureId": "Pastures.Id", "PreviousPastureId": "Pastures.Id"}}, "ActivityPastureMaintenances": {"description": "Records maintenance activities for pastures, including type, cost, and duration.", "columns": {"MaintenanceStartDate": "Start date of maintenance.", "MaintenanceEndDate": "End date of maintenance.", "MaintenancePaymentDate": "Date of payment for maintenance.", "PastureMaintenanceTypeId": "ID linking to the type of maintenance.", "PastureId": "ID linking to the pasture being maintained.", "MaintenanceCost": "Cost associated with maintenance.", "PriceUnitId": "ID linking to the unit for cost.", "ActivityUserId": "ID of the user recording the maintenance.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "PastureMaintenanceTypeId": "activitypasturemaintenancetypes.Id", "ActivityUserId": "AspNetUsers.Id", "PastureId": "Pastures.Id", "PriceUnitId": "units.Id"}}, "ActivityPastureMaintenanceTypes": {"description": "Defines types of pasture maintenance, such as fertilization or fencing.", "columns": {"UserId": "ID of the user defining the maintenance type.", "Name": "Name of the maintenance type.", "Description": "Additional details about the maintenance type."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityMedications": {"description": "Contains medications used in treatments, specific to users.", "columns": {"UserId": "ID of the user owning the medication record.", "Name": "Name of the medication.", "Description": "Description of the medication."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityGroupMovements": {"description": "Logs group movements of cattle, including purpose, date, and user information.", "columns": {"GroupId": "ID linking to the cattle group being moved.", "ActivityPurposeId": "ID linking to the purpose of the movement.", "ActivityUserId": "ID of the user recording the movement.", "MovementDate": "Date of the group movement.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityPurposeId": "activitypurposes.Id", "ActivityUserId": "AspNetUsers.Id", "GroupId": "cattlegroups.Id"}}, "ActivityGroupMovementCattleGroup": {"description": "Defines previous groups involved in cattle group movements.", "columns": {"ActivityGroupMovementsId": "ID linking to the main group movement activity.", "PreviousGroupsId": "ID linking to the previous group involved in the movement."}, "foreign_keys": {"ActivityGroupMovementsId": "activitygroupmovements.Id", "PreviousGroupsId": "cattlegroups.Id"}}, "ActivityFeedTypes": {"description": "Contains types of feed available for animals, such as Grass, Silage, or Grain.", "columns": {"UserId": "ID of the user managing the feed type.", "Name": "Type of feed, e.g., Supplements or Hay.", "Description": "Description of the feed type."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityFeedings": {"description": "Logs feeding activities, including quantity, cost, and feed type.", "columns": {"ActivityDate": "Date of the feeding activity.", "QuantityFeed": "Quantity of feed provided.", "FeedingCost": "Cost associated with the feeding activity.", "ActivityFeedTypeId": "ID linking to the type of feed used.", "ActivityFeedingMethodId": "ID linking to the method of feeding.", "ActivityId": "ID linking to the main activity record.", "PastureId": "ID linking to the pasture where feeding occurs."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityFeedingMethodId": "activityfeedingmethods.Id", "ActivityFeedTypeId": "activityfeedtypes.Id"}}, "ActivityFeedingMethods": {"description": "Defines methods of feeding, such as Grazing, Automatic Feeder, or Manual Feeding.", "columns": {"UserId": "ID of the user defining the feeding method.", "Name": "Method of feeding.", "Description": "Additional description of the feeding method."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityDiagnoses": {"description": "Contains diagnoses used in treatment activities.", "columns": {"UserId": "ID of the user creating the diagnosis.", "Name": "Name of the diagnosis.", "Description": "Description of the diagnosis."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityDeadRecords": {"description": "Logs records of animal deaths, including date and cause.", "columns": {"DeathDate": "Date of death.", "DeathCauseId": "ID linking to the cause of death.", "DeathDetail": "Additional details about the death.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "DeathCauseId": "DeathCauses.Id"}}, "ActivityCastrationTypes": {"description": "Defines castration methods, such as Knife or Emasculator.", "columns": {"UserId": "ID of the user managing castration types.", "Name": "Type of castration, e.g., Scalpel, Burdizzo Clamp.", "Description": "Additional description of the castration type."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityCastrations": {"description": "Records castration activities, including date and type of castration.", "columns": {"CastrationDate": "Date of the castration.", "ActivityCastrationTypeId": "ID linking to the type of castration.", "ActivityId": "ID linking to the main activity record.", "ActivityUserId": "ID of the user performing the castration."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityCastrationTypeId": "activitycastrationtypes.Id", "ActivityUserId": "AspNetUsers.Id"}}, "ActivityCalvings": {"description": "Records calving activities, including breed and calving date.", "columns": {"CalvingDate": "Date of the calving event.", "ActivityBreedId": "ID linking to the breed involved in calving.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ActivityBreedId": "activitybreeds.Id"}}, "ActivityCalvingCalves": {"description": "Associates calves with specific calving activities.", "columns": {"AnimalId": "ID linking to the animal (calf) involved.", "ActivityCalvingId": "ID linking to the calving activity."}, "foreign_keys": {"ActivityCalvingId": "activitycalvings.Id", "AnimalId": "Animals.Id"}}, "ActivityBreeds": {"description": "Records breeding activities, including technician, method, and cost.", "columns": {"BreedingDate": "Date of the breeding activity.", "TechnicianName": "Name of the technician performing the breeding.", "DonorCow": "ID of the donor cow involved.", "MethodId": "ID linking to the breeding method.", "EndDate": "End date of the breeding process.", "Notes": "Additional notes on the breeding activity.", "ActivityId": "ID linking to the main activity record.", "BreedingCost": "Cost associated with breeding.", "PaymentDate": "Date of payment for breeding services.", "SemenContainerId": "ID linking to the container used for semen storage."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "SemenContainerId": "semencontainers.Id"}}, "ActivityBreedingSoundnessExams": {"description": "Records breeding soundness exams, including findings and results.", "columns": {"ActivityDate": "Date of the soundness exam.", "Veterinarian": "Name of the veterinarian conducting the exam.", "FindingId": "ID linking to the exam findings.", "ResultId": "ID linking to the result of the exam.", "ActivityId": "ID linking to the main activity record."}, "foreign_keys": {"ActivitiesPerformedId": "ActivitiesPerformed.Id", "ResultId": "applicationenums.Id", "FindingId": "breedingsoundnessexamfindings.Id"}}, "ActivityBreedBull": {"description": "Associates bulls with breeding activities.", "columns": {"AnimalId": "ID linking to the animal (bull) involved.", "ActivityBreedId": "ID linking to the breeding activity."}, "foreign_keys": {"ActivityBreedId": "ActivityBreeds.Id", "AnimalId": "Animals.Id"}}, "ActivityBehaviorIndicatingHeats": {"description": "Defines behaviors indicating heat in animals, specific to users and ranches.", "columns": {"Name": "Name of the behavior indicating heat.", "UserId": "ID of the user defining the behavior.", "Description": "Additional details about the behavior."}, "foreign_keys": {"UserId": "AspNetUsers.Id"}}, "ActivityBehaviorIndicatingHeatActivityRecordHeat": {"description": "Associates behaviors with heat records.", "columns": {"ActivityBehaviorsIndicatingHeatId": "ID linking to the behavior indicating heat.", "ActivityRecordHeatsId": "ID linking to the heat record."}, "foreign_keys": {"ActivityBehaviorsIndicatingHeatId": "activitybehaviorindicatingheats.Id", "ActivityRecordHeatsId": "activityrecordheats.Id"}}}