import anthropic
import j<PERSON>
from typing import Optional
from db_schema import db_schema_description
from config import CLAUDE_API_KEY


class ClaudeSQLGenerator:
    def __init__(self):
        """
        Initialize the Claude SQL Generator with your API key.

        Args:
        """
        self.client = anthropic.Client(api_key=CLAUDE_API_KEY)
        self.model = "claude-3-haiku-20240307"

    def create_system_prompt(self, user_id: int) -> str:
        """
        Create a system prompt with the database schema.

        Args:
            user_id (int): User id
        """
        return f"""You are a MySQL database assistant. Below is the schema of the database:

        Database Schema:
        {db_schema_description}

        ### Context ###
        Note: When generating SQL queries involving the LiveStock and Cattle tables, keep the following points in mind:
            - The Id column in both Cattle and LiveStock tables represents the same entity. 
            - Always join the two tables on this Id when fetching information across both tables.
            - When applying filter on animal name use Cattle.VisualId.
            - If liveStock BirthDate is not available, use LiveStock.CreatedOn.

        ### Entity Lookups ###
            - Use `LEFT JOIN` for foreign keys (such as PastureId, BreedId, GenderId, etc.) and retrieve the `Name` field from the corresponding lookup tables.
            - For each foreign key column, follow this pattern:
                - Join the foreign key in the `Cattle` table to the corresponding lookup table using its `Id`.
                - Select the `Name` field from the lookup table and alias it appropriately to make it clear which field it represents (e.g., `Pastures.Name AS PastureName` for `PastureId`).
                - Use this pattern for all foreign keys to ensure that names are returned instead of IDs.

        Animal is due for Pregnancy check Activity if it is a cow with exposed status and age > 30 days. 
        Animal is due for Calving Activity if it is a cow, pregnant, and 15-21 days to calving.
        Animal is due for Weightmeasurement Activity check if it is a bull calf and age = 1 month.
        Animal is eligible for Breeding Activity if it is a cow, breed statuses are open or exposed, and age is 15-24 months.
        Animal is eligible for Yearling Activity if it is a bull calf and age = 1 year.
        Animal is eligible for Promote to Bull Activity if it is a bull calf and age ≥ 15 months.
        Animal is eligible for Promote to Cow Activity if it is a steer or heifer and age ≥ 15 months.
        Animal is eligible for  Castration Activity if it is a bull calf and age = 2 months.
        When the user asks questions involving time-based conditions like "soon"  convert these phrases into concrete time intervals, such as 30 days.
        
        When interpreting time-related phrases, convert terms like "this year," "current year," "this month," "current month," "this week," "current week," and "quarter" into appropriate date ranges for SQL queries
        Ensure queries utilize these ranges to remain SARGable and efficient.
        Always apply a limit to show only 20 records like limit 20

        ### Instructions ###
            - When making alias use backticks
            - **Answer user questions by writing SQL SELECT queries based on the database schema provided and ensure you filter results by userId {user_id}.**
            - Use joins and avoid sub queries and always output complete query.
            - The output should be a complete, valid MySQL query written in a single line without unnecessary formatting or line breaks.
            - Ensure all queries have fully formed WHERE clauses with valid conditions and no dangling conjunctions like AND or OR without a following condition.
        """

    def generate_sql_query(
            self,
            user_request: str,
            user_id: int,
    ) -> Optional[str]:
        """
        Generate a SQL query based on the user's request.

        Args:
            user_request (str): Natural language request for the query
            temperature (float): Model temperature (lower for more deterministic results)
            max_tokens (int): Maximum response length

        Returns:
            str: Generated SQL query or None if there's an error
        """
        try:
            system_prompt = self.create_system_prompt(user_id)

            message = self.client.messages.create(
                model=self.model,
                system=system_prompt,
                messages=[{
                    "role": "user",
                    "content": user_request
                }],
                temperature=0.1,
                max_tokens=1000
            )

            return message.content

        except anthropic.APIError as e:
            print(f"API Error: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None


# Example usage
def claude_generate_sql_query(user_query, user_id):
    # Initialize the generator
    sql_generator = ClaudeSQLGenerator()

    # Generate SQL query
    result = sql_generator.generate_sql_query(
        user_query,
        user_id
    )

    if result:
        print("Generated SQL Query:")
        print(result)
    else:
        print("Failed to generate SQL query")

    return result[0].text
