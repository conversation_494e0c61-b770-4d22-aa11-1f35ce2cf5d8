schema_description = """
    - Table: BaseEntity (Id, CreatedOn, ModifiedOn, IsDeleted)
    - Table: Common (Id, Name, Description) 
    - Table: AspNetUsers (Id, UserName, Email, PasswordHash, CreatedOn, ModifiedOn, FullName)
    - Table: LiveStock (Id, **AnimalTypeId**, AnimalGenderId, AnimalStatusId, BirthDate, BirthWeight, AnimalOwnershipTypeId, Weight, SalePrice, DeathCauseDate, OwnersName, PurchaseDate, PurchasePrice, ColorId, IsDeleted)
    - LiveStock Relationships:
        - LiveStock.AnimalTypeId -> AnimalType.Id
        - LiveStock.AnimalGenderId -> AnimalGender.Id
        - LiveStock.AnimalStatusId -> AnimalStatus.Id
        - LiveStock.ColorId -> Unit.Id
        
    - Table: Cattle (Id, VisualId, EID, SerialNumber, IsLock, PastureId, BreedId, BreedStatusId, AnimalConceptionTypeId, SireId, DamId, WeaningDate, WeaningWeight, YearlingDate, *YearlingWeight*, **UserId**, RanchId, CastrationDate, CastrationTypeId, WeaningStatusId, BreedingDate, UnitId, PriceUnitId)
    - CattleRelationships:
    - Cattle.PastureId -> Pasture.Id
    - Cattle.BreedId -> Breed.Id
    - Cattle.BreedStatusId -> BreedStatuses.Id
    - Cattle.SireId -> Cattle.Id
    - Cattle.DamId -> Cattle.Id
    - Cattle.UserId -> AspNetUsers.Id
    - Cattle.RanchId -> Ranch.Id
    - Cattle.CastrationTypeId -> ActivityCastrationType.Id
    - Cattle.WeaningStatusId -> WeaningStatus.Id

    - Table: ActivityBatches (Id, UserId, ActivityTypeId, Notes, *ActivityDate*)
    - The `ActivityBatches` table stores information about when activity is performed it has one to many relationship with Activities and each in activity has one animal. So if activity is performed on 10 animals then 10 records will be saved in Activities table and one will be saved in ActivityBatches.
    - ActivityBatches Relationships:
      - ActivityBatches.UserId -> ApplicationUsers.Id
      - ActivityBatches.ActivityTypeId -> ActivityTypes.Id
      - ActivityBatches has many Activities (one-to-many relationship)

    - Table: Activities (Id, ActivityBatchId, AnimalIdentifier, CattleId)
    - Activities Relationships:
      - Activities.ActivityBatchId -> ActivityBatches.Id (belongs to one ActivityBatch)
      - Activities.CattleId -> Cattle.Id (optional, foreign key to Cattles table)
    - **Note**: filter use ActivityBatches.ActivityDate  ActivityBatches.ActivityTypeId.


    - Additional Activity Type Tables:
      - Table: ActivityPastureMovements (Id, ActivityId -> Activities.Id, )
      - Table: ActivityGroupMovements (Id, ActivityId -> Activities.Id, )
      - Table: ActivityTreatments (Id, ActivityId -> Activities.Id, )
      - Table: ActivityWeightMeasurements (Id, ActivityId -> Activities.Id, )
      - Table: ActivityWeaning (Id, ActivityId -> Activities.Id, )
      - Table: ActivityYearling (Id, ActivityId -> Activities.Id, )
      - Animal castration records are save in ActivityCastrations table (Id, ActivityId -> Activities.Id, CastrationDate)
      - Table: ActivitySales (Id, ActivityId -> Activities.Id, )
      - Table: ActivityCalvings (Id, ActivityId -> Activities.Id)
      - Table: ActivityAddGroups (Id, ActivityId -> Activities.Id, )
      - Table: ActivityPregnancyChecks (Id, ActivityId -> Activities.Id, )
      - Table: ActivityBreeds (Id, ActivityId -> Activities.Id)
        - The ActivityBreeds joins with Activities as JOIN `Activities` ON `ActivityBreeds`.ActivityId = `Activities`.Id 
        - The `ActivityBreeds` table stores information about breeding records associated with an activity. It has a one-to-many relationship with the `ActivityBreedBull` table.
      - Table: `ActivityBreedBull` (Id, ActivityBreedId -> ActivityBreeds.Id, AnimalId -> is Bull id)
        - The `ActivityBreedBull` table stores information about bulls related to a breed in `ActivityBreeds`. Each `ActivityBreeds.Id` can have multiple corresponding rows in the `ActivityBreedBull` table.
      - Table: ActivityDeadRecords (Id, ActivityId -> Activities.Id, )
      - Table: ActivityObservations (Id, ActivityId -> Activities.Id, )
      - Table: ActivityPastureMaintenances (Id, ActivityId -> Activities.Id, )
      - Table: ActivityBreedingSoundnessExams (Id, ActivityId -> Activities.Id, )
      - Table: ActivityRecordHeats (Id, ActivityId -> Activities.Id, )
      - Table: ActivitySemenCollections (Id, ActivityId -> Activities.Id, )
      - Table: ActivityFeedings (Id, ActivityId -> Activities.Id, )
      - Table: ActivityWithdrawals (Id, ActivityId -> Activities.Id, )
      - Table: ActivityVaccinations (Id, ActivityId -> Activities.Id, )

    - Table: CattleGroups (Id, UserId, CreatedOn, ModifiedOn, IsDeleted, Name, Description, RanchId)
      - Relationships:
        - CattleGroups.UserId -> AspNetUsers.Id
        - CattleGroups.RanchId -> Ranches.Id

    - Table: PastureFrequencies (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Frequencies: Monthly, Yearly

    - Table: PastureOwnerships (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Ownership Types: Owned, Leased, Rented, Purchased

    - Table: Pastures (Id, RanchId, UserId, Area, CreatedOn, ModifiedOn, IsDeleted, Name, Description, AreaUnitId, AdministratedBy, Cost, PastureFrequencyId, PastureOwnershipId, PaymentDate, AreaPolygon, CentralPoint)
      - Relationships:
        - Pastures.RanchId -> Ranches.Id
        - Pastures.UserId -> AspNetUsers.Id
        - Pastures.AreaUnitId -> Units.Id
        - Pastures.PastureFrequencyId -> PastureFrequencies.Id
        - Pastures.PastureOwnershipId -> PastureOwnerships.Id

    - Table: Finances (Id, FinanceDetailId, RecordDate, PaymentDate, Notes, Amount, PriceUnitId, AdministratedById, CreatedOn, ModifiedOn, IsDeleted, ActivityBatchId, CattleId, PastureId)
      - Records: Financial transactions related to income and expenses
      - Relationships:
        - Finances.FinanceDetailId -> FinanceDetails.Id
        - Finances.AdministratedById -> AspNetUsers.Id
        - Finances.CattleId -> Cattle.Id
        - Finances.PastureId -> Pastures.Id
        - Finances.PriceUnitId -> Units.Id
        - Finances.ActivityBatchId -> ActivityBatches.Id

    - Table: FinanceTypes (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Types: Income, Expense

    - Table: FinanceDetails (Id, UserId, RanchId, FinanceTypeId, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Records: Detailed records of financial transactions, categorized by type (income or expense)
      - Relationships:
        - FinanceDetails.UserId -> AspNetUsers.Id
        - FinanceDetails.FinanceTypeId -> FinanceTypes.Id
        - FinanceDetails.RanchId -> Ranches.Id

    - Table: AnimalTypes (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - *Animal Types: Bull, Cow, Calf*
      - Relationships:
        - AnimalTypes.Id -> LiveStock.AnimalTypeId
    - Table: CattleCattleGroup (AnimalsId, CattleGroupsId)
      - Relationships:
        - CattleCattleGroup.AnimalsId -> Cattle.Id
        - CattleCattleGroup.CattleGroupsId -> CattleGroups.Id

    - Table: AnimalStatuses (Id, NewAnimal, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Animal Statuses: Sold, Reference, Active, Dead

    - Table: AnimalGenders (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Animal Genders: Bullcalf, Heifer, Steer

    - Table: Breeds (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)

    - Table: BreedStatuses (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Status Names: Exposed, Open, Pregnant

    - Table: AnimalConceptionTypes (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)

    - Table: Ranches (Id, UserId, CreatedOn, ModifiedOn, IsDeleted, Name, Description)
      - Relationships:
        - Ranches.UserId -> AspNetUsers.Id

    - Table: RanchUsers (UserId, RanchId, RoleId, CreatedOn, ModifiedOn, IsDeleted, IsActive)
      - Relationships:
        - RanchUsers.UserId -> AspNetUsers.Id
        - RanchUsers.RanchId -> Ranches.Id
        - RanchUsers.RoleId -> AspNetRoles.Id

    - Table: AnimalOwnershipTypes (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)

    - Table: TaskTypes (Id, SendMail, SendNotification, IsPublic, EmailTemplateId, CreatedOn, ModifiedOn, IsDeleted, Name, Description, RanchId, IsClickable)
      - Relationships:
        - TaskTypes.EmailTemplateId -> EmailTemplates.Id
        - TaskTypes.RanchId -> Ranches.Id

    - Table: TaskStatuses (Id, CreatedOn, ModifiedOn, IsDeleted, Name, Description)

    - Table: Tasks (Id, TaskTypeId, TaskStatusId, DueDate, IsMailSent, IsNotificationSent, Description, CreatedOn, ModifiedOn, IsDeleted, IsSystemGenerated, RemindMeDate, Name, RanchId, UserId, IsRead, IsView)
      - Relationships:
        - Tasks.UserId -> AspNetUsers.Id
        - Tasks.RanchId -> Ranches.Id
        - Tasks.TaskStatusId -> TaskStatuses.Id
        - Tasks.TaskTypeId -> TaskTypes.Id

    - Table: TaskMorphs (TaskId, EntityId, EntityName, MorphStatusId)
      - Relationships:
        - TaskMorphs.TaskId -> Tasks.Id
        - TaskMorphs.MorphStatusId -> TaskStatuses.Id

    """
