aiohappyeyeballs==2.4.3
aiohttp==3.10.9
aiosignal==1.3.1
annotated-types==0.7.0
anthropic==0.37.1
anyio==4.6.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
async-timeout==5.0.1
attrs==24.2.0
babel==2.16.0
beautifulsoup4==4.12.3
bleach==6.1.0
blinker==1.8.2
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
debugpy==1.8.6
decorator==5.1.1
defusedxml==0.7.1
distro==1.9.0
durationpy==0.9
exception==0.1.0
executing==2.1.0
faiss-cpu==1.7.4
fastjsonschema==2.20.0
filelock==3.16.1
flasgger==*******
Flask==3.0.3
Flask-Cors==5.0.0
flatbuffers==24.3.25
fqdn==1.5.1
frozenlist==1.4.1
fsspec==2024.10.0
google-ai-generativelanguage==0.6.10
google-api-core==2.21.0
google-api-python-client==2.149.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.3
googleapis-common-protos==1.65.0
greenlet==3.1.1
grpcio==1.66.2
grpcio-status==1.66.2
h11==0.14.0
httpcore==1.0.6
httplib2==0.22.0
httpx==0.27.2
huggingface-hub==0.26.2
humanfriendly==10.0
idna==3.10
ipykernel==6.29.5
ipython==8.28.0
isoduration==20.11.0
itsdangerous==2.2.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.6.1
joblib==1.4.2
json5==0.9.25
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
langchain==0.3.3
langchain-core==0.3.10
langchain-google-genai==2.0.1
langchain-text-splitters==0.3.0
langsmith==0.1.133
lxml==5.3.0
MarkupSafe==3.0.1
matplotlib-inline==0.1.7
mistune==3.0.2
ml-dtypes==0.4.1
monotonic==1.6
mpmath==1.3.0
multidict==6.1.0
mysql-connector-python==9.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
notebook==7.2.2
notebook_shim==0.2.4
numpy==1.26.4
openai==1.51.2
orjson==3.10.7
overrides==7.7.0
packaging==24.1
pandocfilters==1.5.1
parso==0.8.4
pillow==11.0.0
platformdirs==4.3.6
prometheus_client==0.21.0
prompt_toolkit==3.0.48
propcache==0.2.0
proto-plus==1.24.0
protobuf==5.28.3
psutil==6.0.0
pure_eval==0.2.3
pyarrow==17.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
Pygments==2.18.0
Pympler==1.1
PyMySQL==1.1.1
pyparsing==3.1.4
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.0
python-json-logger==2.0.7
pytz==2024.2
pywin32==307
pywinpty==2.0.13
PyYAML==6.0.2
pyzmq==26.2.0
redis==5.2.1
redisvl==0.3.9
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.20.0
rsa==4.9
safetensors==0.4.5
scikit-learn==1.6.1
scipy==1.14.1
Send2Trash==1.8.3
sentence-transformers==3.4.1
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.35
stack-data==0.6.3
sympy==1.13.1
tabulate==0.9.0
tenacity==8.5.0
terminado==0.18.1
threadpoolctl==3.5.0
tinycss2==1.3.0
tokenizers==0.21.0
toml==0.10.2
toolz==1.0.0
torch==2.6.0
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
transformers==4.48.3
types-python-dateutil==2.9.0.20241003
typing_extensions==4.12.2
tzdata==2024.2
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.3
validators==0.34.0
watchdog==5.0.3
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==13.1
Werkzeug==3.0.4
wrapt==1.16.0
yarl==1.14.0
zipp==3.20.2
