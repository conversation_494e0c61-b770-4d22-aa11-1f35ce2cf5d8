import openai
import mysql.connector
import logging
from config import DATABASE_CONFIG


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Initialize OpenAI API
openai.api_key = '********************************************************'


# Connect to MySQL Database
def connect_to_db():
    return mysql.connector.connect(
        host=DATABASE_CONFIG["host"],
        user=DATABASE_CONFIG["user"],
        password=DATABASE_CONFIG["password"],
        database=DATABASE_CONFIG["database"]
    )

def generate_sql_query(user_query, schema_description):

    logging.info("Starting SQL query generation.")
    logging.info("User Query: %s", user_query)

    # Define the system message with schema description
    system_message = f"""
    You are a MySQL database assistant. Below is the schema of the database:
    {schema_description}
    Answer user questions by writing SQL SELECT queries based on the database schema provided. 
    You can only write SELECT statements.
    The output query should be in a single line no formatting is required.
    Use joins and avoid sub queries
    To return data related to that user.
    """
    logging.info("Sending request to OpenAI API.")

    # Create a chat completion request
    response = openai.chat.completions.create(
        model="gpt-3.5-turbo",  # Specify your model
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_query},
        ],
        max_tokens=150,
        temperature=0.2,
    )

    # Extract the response text
    sql_query = response.choices[0].message.content
    logging.info("SQL Query Generated: %s", sql_query)
    return sql_query

# Function to execute SQL query
def execute_sql_query(query):
    db = connect_to_db()
    cursor = db.cursor()

    try:
        cursor.execute(query)
        results = cursor.fetchall()
        return results
    except mysql.connector.Error as err:
        return f"Error: {err}"
    finally:
        cursor.close()
        db.close()


# Main function to process user queries
def process_user_query(user_query):
    schema_description = """
    - Table: BaseEntity (Id, OrganizationId, CreatedOn, ModifiedOn, CreatedById, ModifiedById, IPAddress, IsDeleted, ModifiedByIPAddress)
    - Table: Common (Id, Name, Description) 
    - Table: AspNetUsers (Id, UserName, Email, PasswordHash, CreatedOn, ModifiedOn, FullName)
    - Table: Organization (Id, OrganizationName, CreatedOn, ModifiedOn)

    - Table: LiveStock (Id, SpeciesId, AnimalTypeId, AnimalGenderId, AnimalStatusId, BirthDate, BirthWeight, AnimalOwnershipTypeId, Weight, SalePrice, DeathCauseDate, OwnersName, PurchaseDate, PurchasePrice, Name, ColorId, CreatedById, ModifiedById)
    - Table: Cattle (Id, VisualId, EID, SerialNumber, IsLock, PastureId, BreedId, BreedStatusId, AnimalConceptionTypeId, SireId, DamId, WeaningDate, WeaningWeight, YearlingDate, YearlingWeight, UserId, RanchId, CastrationDate, CastrationTypeId, WeaningStatusId, BreedingDate, UnitId, PriceUnitId)

    Relationships:
    - LiveStock.SpeciesId -> Species.Id
    - LiveStock.AnimalTypeId -> AnimalType.Id
    - LiveStock.AnimalGenderId -> AnimalGender.Id
    - LiveStock.AnimalStatusId -> AnimalStatus.Id
    - LiveStock.AnimalOwnershipTypeId -> AnimalOwnershipType.Id
    - LiveStock.ColorId -> Unit.Id
    - LiveStock.CreatedById -> AspNetUsers.Id
    - LiveStock.ModifiedById -> AspNetUsers.Id
    - LiveStock.OrganizationId -> Organization.Id

    - Cattle.PastureId -> Pasture.Id
    - Cattle.BreedId -> Breed.Id
    - Cattle.BreedStatusId -> BreedStatus.Id
    - Cattle.AnimalConceptionTypeId -> AnimalConceptionType.Id
    - Cattle.SireId -> Cattle.Id
    - Cattle.DamId -> Cattle.Id
    - Cattle.UserId -> AspNetUsers.Id
    - Cattle.RanchId -> Ranch.Id
    - Cattle.CastrationTypeId -> ActivityCastrationType.Id
    - Cattle.WeaningStatusId -> WeaningStatus.Id
    - Cattle.UnitId -> Unit.Id
    - Cattle.PriceUnitId -> Unit.Id
    
    
    - Table: ActivityBatches (Id, UserId, ActivityTypeId, Notes, ActivityDate, ActivityCreatedOn)
    - Relationships:
      - ActivityBatches.UserId -> ApplicationUsers.Id
      - ActivityBatches.ActivityTypeId -> ActivityTypes.Id
      - ActivityBatches.Attachments (optional, one-to-many relationship)

    - Table: Activities (Id, ActivityBatchId, AnimalIdentifier, CattleId)
    - Relationships:
      - Activities.ActivityBatchId -> ActivityBatches.Id
      - Activities.CattleId -> Cattles.Id (optional, foreign key to Cattles table)

    - Additional Activity Types:
      - ActivityPastureMovements (Id, ActivityId, ...)
      - ActivityGroupMovements (Id, ActivityId, ...)
      - ActivityTreatments (Id, ActivityId, ...)
      - ActivityWeightMeasurements (Id, ActivityId, ...)
      - ActivityWeaning (Id, ActivityId, ...)
      - ActivityYearling (Id, ActivityId, ...)
      - ActivityCastrations (Id, ActivityId, ...)
      - ActivitySales (Id, ActivityId, ...)
      - ActivityCalvings (Id, ActivityId, ...)
      - ActivityAddGroups (Id, ActivityId, ...)
      - ActivityPregnancyChecks (Id, ActivityId, ...)
      - ActivityBreeds (Id, ActivityId, ...)
      - ActivityDeadRecords (Id, ActivityId, ...)
      - ActivityObservations (Id, ActivityId, ...)
      - ActivityPromotions (Id, ActivityId, ...)
      - ActivitySetToReferences (Id, ActivityId, ...)
      - ActivityPastureMaintenances (Id, ActivityId, ...)
      - ActivityBreedingSoundnessExams (Id, ActivityId, ...)
      - ActivityRecordHeats (Id, ActivityId, ...)
      - ActivitySemenCollections (Id, ActivityId, ...)
      - ActivityMilkingSessions (Id, ActivityId, ...)
      - ActivityFeedings (Id, ActivityId, ...)
      - ActivityWithdrawals (Id, ActivityId, ...)
      - ActivityVaccinations (Id, ActivityId, ...)
      - ActivityBodyConditionScores (Id, ActivityId, ...)
      - ActivityMilkEvaluations (Id, ActivityId, ...)
    """

    # Generate SQL query
    sql_query = generate_sql_query(user_query, schema_description)
    print(f"Generated SQL Query: {sql_query}")

    # Execute SQL query
    results = execute_sql_query(sql_query)
    return results


# Example usage
user_query = "Get count of weight measurement activity?"
output = process_user_query(user_query)
print(output)
