# openai_helper.py
import os
import openai
import logging
from config import OPENAI_API_KEY
from db import execute_sql_query
import json
from llm_session_manager.standard import StandardLLMSessionManager
from schema import schema_description
from commonHelper import FileUtility, StringUtility
import json

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

# Initialize OpenAI API
openai.api_key = OPENAI_API_KEY


def generate_sql_query(user_query, user_id, model):
    logging.info("Starting SQL query generation.")
    logging.info("User Query: %s", user_query)

    # Define the system message with schema description
    system_message = f"""
    You are a MySQL database assistant. Below is the schema of the database:
    {schema_description}
    
    ### Context ###
    Note: When generating SQL queries involving the LiveStock and Cattle tables, keep the following points in mind:
        - The Id column in both Cattle and LiveStock tables represents the same entity. 
        - Always join the two tables on this Id when fetching information across both tables.
        - Columns specific to the LiveStock table include: (Id, SpeciesId, AnimalTypeId, AnimalGenderId, AnimalStatusId, BirthDate, BirthWeight, AnimalOwnershipTypeId, Weight, SalePrice, DeathCauseDate, OwnersName, PurchaseDate, PurchasePrice, Name, ColorId, IsDeleted). These columns should be fetched or filtered only from the LiveStock table.
        - Columns specific to the Cattle table include: (Id, *VisualId*, EID, SerialNumber, IsLock, PastureId, BreedId, BreedStatusId, AnimalConceptionTypeId, SireId, DamId, WeaningDate, WeaningWeight, YearlingDate, YearlingWeight, UserId, RanchId, CastrationDate, CastrationTypeId, WeaningStatusId, BreedingDate, UnitId, PriceUnitId). These columns should be fetched or filtered only from the Cattle table.
        - When checking for deleted records, always filter by LiveStock.IsDeleted = 0.
        - When applying filter on animal name use Cattle.VisualId.
        - always filter using the UserId column from the Cattle table.
    
    Cattle are due for pregnancy checks if their breed status is 'open' or 'exposed' and their birthdate (from the Livestock table) is more than 30 days old
    When the user asks questions involving time-based conditions like "soon"  convert these phrases into concrete time intervals, such as 30 days.
    
    When interpreting time-related phrases, convert terms like "this year," "current year," "this month," "current month," "this week," "current week," and "quarter" into appropriate date ranges for SQL queries:
        - For "this year" or "current year," use a date range: column BETWEEN DATE_FORMAT(CURDATE(), '%Y-01-01') AND LAST_DAY(DATE_FORMAT(CURDATE(), '%Y-12-31')) is the current year.
        - For "this month" or "current month," use: column  BETWEEN DATE_FORMAT(CURDATE(), '%Y-%m-01') AND LAST_DAY(CURDATE())  is the current year and month.
        - For "this week" or "current week," use: Column BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)`.
        - For "this quarter," define the ranges based on the current date.

    Ensure queries utilize these ranges to remain SARGable and efficient.
    if user request list of records or all records then apply limit to show only 20 records like limit 20



    ### Instructions ###
        - When making alias use backticks
        - **Answer user questions by writing SQL SELECT queries based on the database schema provided and ensure you filter results by userId {user_id}.**
        - Use joins and avoid sub queries and always output complete query.
        - The output should be only MySQL query and should be in a single line no formatting is required.
        - Ensure all queries have fully formed WHERE clauses with valid conditions and no dangling conjunctions like AND or OR without a following condition.

    """
    logging.info("Sending request to OpenAI API.")
    logging.info("system_message")
    logging.info("%s", system_message)

    # Create a chat completion request
    response = openai.chat.completions.create(
        model=model,  # Specify your model
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_query},
        ],
        max_tokens=150,
        temperature=0.7,
    )

    # Extract the response text
    sql_query = response.choices[0].message.content
    logging.info("SQL Query Generated: %s", sql_query)
    return sql_query


def generate_sql_query_fine_tune(user_query, user_id, model):
    logging.info("Starting SQL query generation.")
    logging.info("User Query: %s", user_query)

    # Define the system message with schema description
    system_message = f"""
    ### Instructions ###
        - When making alias use backticks
        - **Answer user questions by writing SQL SELECT queries based on the database schema provided and ensure you filter results by userId {user_id}.**
    """
    logging.info("Sending request to OpenAI API.")
    logging.info("system_message")
    logging.info("%s", system_message)

    # Create a chat completion request
    response = openai.chat.completions.create(
        model=model,  # Specify your model
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_query},
        ],
        max_tokens=150,
        temperature=0.7,
    )

    # Extract the response text
    sql_query = response.choices[0].message.content
    logging.info("SQL Query Generated: %s", sql_query)
    return sql_query


def generate_agent_response(user_query, sql_query, results, model):
    logging.info("Generating human-readable response.")
    if isinstance(results, list) and len(results) > 20:
        results = results[:20]
    #    SQL query used: {sql_query}
    #     if sql query has limit clause at the end then say I can show only x amount records as i am still learning the amount will be count of query results
    system_message = """
    You are an AI assistant that interprets SQL query results and explains them in natural language. 
    Given a user's question, the SQL query used, and the query results, provide a clear and concise explanation.
    If there is an error in results then say i am unable to get that information for your request please ask other questions
    If user asks about yourself then say I am an AI assistant that helps you to chat with your data.
    if results count is 20 then say I can show only x amount records as i am still learning the amount will be count of results
    """

    user_message = f"""
    User question: {user_query}
    Query results: {results}
    Please provide a human-readable interpretation of these results.
    """

    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message},
        ],
        max_tokens=200,
        temperature=0.7,
    )

    agent_response = response.choices[0].message.content
    logging.info("Human-readable response generated.")
    return agent_response


def genai_function_calling(user_query, user_id):
    # Define the system message with schema description
    system_message = f"""
        You are a MySQL database assistant. Below is the schema of the database:
        {schema_description}
        Note: `Cattle table Id` and `Livestock table Id` refer to the same entity, so treat them as equivalent when generating SQL queries or interpreting results.
        Make sure to read the schema of Cattle and Livestock tables because they both form single cattle entity and some columns are in livestock and rest in Cattle like to identify deleted cattle should be checked in livestock.
        When making alias use backticks
        Cattle are due for pregnancy checks if their breed status is 'open' or 'exposed' and their birthdate (from the Livestock table) is more than 30 days old

        Answer user questions by writing SQL SELECT queries based on the database schema provided and ensure you filter results by userId {user_id}. 
        You can only write SELECT statements.
        The output query should be in a single line no formatting is required.
        Use joins and avoid sub queries
        To return data related to that user.
        """

    # Define the function for executing SQL queries
    functions = [
        {
            "name": "execute_sql_query",
            "description": "Executes the given SQL query on the MySQL database and returns the result.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The SQL query to execute.",
                    }
                },
                "required": ["query"],
            },
        }
    ]

    # Create the GPT call to generate SQL query and handle the function call to fetch results
    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_query},
        ],
        functions=functions,
        function_call="auto",  # Allow GPT to decide when to call the function
        max_tokens=300,  # Increased token limit for larger results
        temperature=0.2,
    )
    # Check if a function call was made (SQL execution)
    logging.info(
        "response.choices[0].finish_reason %s", response.choices[0].finish_reason
    )
    if response.choices[0].finish_reason == "function_call":
        function_call = response.choices[0].message.function_call

        logging.info("Function call made %s", function_call)

        if function_call.name == "execute_sql_query":
            # GPT-generated SQL query
            sql_query = json.loads(function_call.arguments)["query"]
            logging.info(f"SQL Query Generated: {sql_query}")

            # Execute the SQL query using the function in Python
            db_results = execute_sql_query(sql_query)

            # Create user-friendly response using the same GPT call
            system_message_for_readable = """
               You are an AI assistant that interprets SQL query results and explains them in natural language. 
               Given a user's question, the SQL query used, and the query results, provide a clear and concise explanation.
               If value is vector give html markup for table.
               I need output in json object in which one key is readable and second key will have html response.
               If there is an error in query or no data is returned then don't show the real error and say apologise i am still learning.
               """

            user_message_for_readable = f"""
            User question: {user_query}
            SQL query used: {sql_query}
            Query results: {db_results}

            Please provide a human-readable interpretation of these results.
            """

            # Add new messages to the same GPT call to generate human-readable response
            completion = openai.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message_for_readable},
                    {"role": "user", "content": user_message_for_readable},
                ],
                max_tokens=300,
                temperature=0.7,
            )

            agent_response = completion.choices[0].message.content
            logging.info("Human-readable response generated.")

            return {
                "sql_query": sql_query,
                "results": db_results,
                "agent_response": agent_response,
            }

    return {"error": "Function call was not made or failed."}


def generate_json_response(chatId, entity, user_prompt, userId, model="gpt-4o-mini"):
    jsonSchema = FileUtility.return_json_schema(
        FileUtility.get_file_name(entity, "action-agent-prompts/Functions")
    )

    session_manager = StandardLLMSessionManager(session_tag=f"{userId}:{chatId}")
    previousConversation = session_manager.get_recent(top_k=20)

    if previousConversation is None:
        previousConversation = []

    # Read Entity Specific message template from file
    if os.path.exists(f"action-agent-prompts/Functions/SystemMessages/{entity}.txt"):
        with open(
            f"action-agent-prompts/Functions/SystemMessages/{entity}.txt", "r"
        ) as system_prompt_file:
            entity_system_message = system_prompt_file.read()

    else:
        entity_system_message = ""

    # Read system message template from file
    with open("action-agent-prompts/system_message.txt", "r") as file:
        system_message = file.read().format(
            e=entity, entitySpecificMessage=entity_system_message, userId=userId
        )

    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": system_message},
            *previousConversation,
            {"role": "user", "content": user_prompt},
        ],
        functions=[
            {
                "name": "generate_entity_data",
                "description": "Generate a JSON payload for creating a new {0} entity.".format(
                    entity
                ),
                "parameters": jsonSchema,
            }
        ],
        function_call="auto",
        max_tokens=200,
        temperature=0.4,
    )

    print("response", response)
    try:
        if response.choices[0].message.function_call is not None:
            entity_data = response.choices[0].message.function_call.arguments
            status = 1

        elif response.choices[0].message.content is not None:
            entity_data = response.choices[0].message.content
            status = 0

    except Exception as e:
        logging.error("Error processing response: %s", str(e))
        entity_data = {"error": "Failed to process the response."}

    print("Output", entity_data)
    session_manager.store(prompt=user_prompt, response=entity_data)

    return {"results": entity_data, "status": status}


def generate_tutorial_recommendation(chatId, userId, user_prompt, model="gpt-4o-mini"):
    """
    Generate tutorial video recommendations based on user prompt.
    Returns a user-friendly response with relevant tutorial videos.
    """
    try:
        session_manager = StandardLLMSessionManager(session_tag=f"{userId}:{chatId}")
        previousConversation = session_manager.get_recent(top_k=20)

        if previousConversation is None:
            previousConversation = []

        # Fetch tutorial videos from database
        sql_query = """
        SELECT VALUE FROM `ConfigurationDetails` cd
        INNER JOIN `ConfigurationEnums` ce ON ce.Id = cd.ConfigurationEnumId
        INNER JOIN `ConfigurationCategories` cc ON cc.Id = ce.ConfigurationCategoryId
        WHERE cc.Name = 'KnowledgeBase'
        """
        raw_videos = execute_sql_query(sql_query)
        
        if not raw_videos:
            return {
                "response": "I apologize, but I couldn't find any tutorial videos at the moment. Please try again later or contact support for assistance.",
                "videos": []
            }

        # Parse JSON data from Value column
        videos = []
        for raw_video in raw_videos:
            try:
                raw_value = raw_video['VALUE']
                if isinstance(raw_value, str):
                    fixed_value = StringUtility.fix_to_valid_json(raw_value)
                    video_data = json.loads(fixed_value)
                else:
                    video_data = raw_value
                
                videos.append({
                    'title': video_data.get('title', ''),
                    'url': video_data.get('VideoUrl', ''),
                    'categories': video_data.get('Categories', [])
                })
            except json.JSONDecodeError:
                logging.error(f"Failed to parse video data: {raw_video['VALUE']}")
                continue

        if not videos:
            return {
                "response": "I apologize, but I couldn't find any valid tutorial videos at the moment. Please try again later or contact support for assistance.",
                "videos": []
            }

        # Create system message for tutorial search
        system_message = """You are a helpful assistant that helps farmers find relevant Cattlytics tutorial videos.
        Your task is to:
        1. Analyze the user's question and understand their needs
        2. Review the available tutorial videos
        3. Select the most relevant videos that would help answer their question
        4. Provide a natural, conversational response that:
           - Acknowledges their question
           - Explains why the recommended videos are helpful
           - Provides a brief overview of what they'll learn
        5. Return your response in a <p> tag, and include the video URLs in <a> tags. Use break lines and bold text where appropriate.
            - Links should be clickable and open in a new tab
        6. If no videos are relevant, explain that and suggest what they might try instead
        
        Focus on agricultural and livestock management context when determining relevance.
        Make your response sound natural and conversational, as if you're talking directly to the farmer."""

        # Format videos list for the prompt
        videos_text = "\n".join([
            f"Title: {v['title']}\n"
            f"Categories: {', '.join(v['categories'])}\n"
            f"URL: {v['url']}\n"
            for v in videos
        ])

        user_message = f"""User question: {user_prompt}

Available tutorial videos:
{videos_text}

Please provide a helpful response that recommends the most relevant videos and explains why they would be useful."""

        # Call OpenAI API
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_message},
                *previousConversation,
                {"role": "user", "content": user_message}
            ],
            max_tokens=800,
            temperature=0.7
        )

        # Get the response text
        response_text = response.choices[0].message.content

        # Extract video URLs from the response
        recommended_videos = []
        for video in videos:
            if video['url'] in response_text:
                recommended_videos.append({
                    'url': video['url'],
                    'title': video['title'],
                    'categories': video['categories']
                })

        session_manager.store(prompt=user_prompt, response=response_text)

        return {
            "response": response_text,
            "videos": recommended_videos
        }

    except Exception as e:
        logging.error(f"Error in generate_tutorial_recommendation: {str(e)}")
        return {
            "response": "I apologize, but I encountered an error while searching for tutorial videos. Please try again later or contact support for assistance.",
            "videos": []
        }
