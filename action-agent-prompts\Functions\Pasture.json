{"type": "object", "properties": {"Name": {"type": "string", "description": "The name of the pasture", "minLength": 1}, "Acres": {"type": "number", "description": "Area of the pasture. This could be in any unit like hector or acrea, whatever the number is add it to the Acres Field irrespective of the unit and conversion"}, "PastureOwnershipId": {"type": "integer", "enum": [1, 2, 3, 4], "description": "Ownership type: 1=Owned, 2=Rented, 3=Leased, 4=Purchased", "default": 1}, "Cost": {"type": "number", "description": "Cost associated with the pasture (optional, defaults to 0)", "default": 0}, "PastureFrequencyId": {"type": "integer", "enum": [1, 2], "description": "Frequency type for pasture usage: 1=Monthly, 2=Yearly", "default": 1}}, "required": ["Name", "PastureOwnershipId"]}