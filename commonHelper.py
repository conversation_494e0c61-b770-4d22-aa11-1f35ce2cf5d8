from datetime import datetime, timedelta
import json
import os
import re

class FileUtility:
    @staticmethod
    def calculate_relative_date(relative_date_str):
        # Handle simple relative dates, e.g., "1 month ago"
        match = re.match(r"(\d+) (day|month|year)s? ago", relative_date_str)
        if match:
            quantity = int(match.group(1))
            unit = match.group(2)
            if unit == "day":
                return (datetime.now() - timedelta(days=quantity)).strftime("%Y-%m-%d")
            elif unit == "month":
                return (datetime.now() - timedelta(days=30 * quantity)).strftime("%Y-%m-%d")
            elif unit == "year":
                return (datetime.now() - timedelta(days=365 * quantity)).strftime("%Y-%m-%d")
        return None

    @staticmethod
    def get_file_name(file, folder='action-agent-prompts', ext='json'):
        return f'{folder}/{file}.{ext}'

    @staticmethod
    def return_json_schema(file_name):
        if os.path.exists(file_name):
            with open(file_name, 'r') as f:
                return json.load(f)
        return None

    @staticmethod
    def save_conversation(messages, filename):
        # Save the messages to a JSON file
        with open(filename, 'w') as file:
            json.dump(messages, file, indent=2)

    @staticmethod
    def add_message_to_thread(messages, role, content, file_name):
        # Append the new message to the list
        messages.append({"role": role, "content": content})
        # Save updated conversation
        FileUtility.save_conversation(messages, file_name)

class StringUtility:
    @staticmethod
    def fix_to_valid_json(invalid_json_str):
        # Add quotes around property names
        fixed = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', invalid_json_str)
        return fixed

    @staticmethod
    def format_string(text):
        # Format the string to be more readable
        return text.strip().capitalize()