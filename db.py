# db_connection.py
import mysql.connector
from config import DATABASE_CONFIG


def connect_to_db():
    return mysql.connector.connect(
        host=DATABASE_CONFIG["host"],
        user=DATABASE_CONFIG["user"],
        password=DATABASE_CONFIG["password"],
        database=DATABASE_CONFIG["database"]
    )


def execute_sql_query(query):
    db = connect_to_db()
    cursor = db.cursor(dictionary=True)  # This will return rows as dictionaries with column names as keys

    try:
        cursor.execute(query)
        results = cursor.fetchall()

        if cursor.description:  # Check if the result set has column descriptions
            columns = [col[0] for col in cursor.description]  # Get column names

            # If the result is scalar (like COUNT(*)), return a scalar response
            if len(results) == 1 and len(results[0]) == 1:
                return {"result": list(results[0].values())[0]}

            # Return results in a JSON-friendly format (list of dictionaries)
            return [dict(zip(columns, row.values())) for row in results]
        else:
            return {"result": "No data returned"}

    except mysql.connector.Error as err:
        return {"error": f"Error: {err}"}
    finally:
        cursor.close()
        db.close()
