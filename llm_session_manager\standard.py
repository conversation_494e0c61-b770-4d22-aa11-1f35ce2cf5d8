from abc import ABC
from typing import Optional
from redisvl.extensions.session_manager import (
    StandardSessionManager,
)

from llm_session_manager.base import BaseLLMSessionManager

class StandardLLMSessionManager(BaseLLMSessionManager):
    """
    Session manager using RedisVL's StandardSessionManager for traditional session management.
    """

    def _initialize_manager(
        self, prefix: str, session_tag: Optional[str], redis_url: str
    ) -> StandardSessionManager:
        return StandardSessionManager(
            name=prefix,
            session_tag=session_tag,
            redis_url=redis_url,
        )
